<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no">
    <title>乐享友邻 - 发布农产品</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-separator: rgba(60,60,67,0.1);
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: rgba(60,60,67,0.6); /* 60% opacity */
            --ios-tertiaryLabel: rgba(60,60,67,0.3); /* 30% opacity */
            --ios-quaternaryLabel: rgba(60,60,67,0.18); /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
            
            /* iOS圆角半径 */
            --ios-corner-radius-small: 6px;
            --ios-corner-radius-medium: 10px;
            --ios-corner-radius-large: 14px;
            --ios-corner-radius-xl: 18px;
        }

        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
            touch-action: manipulation;
            user-select: none;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            background-color: var(--ios-card);
            border-radius: var(--ios-corner-radius-large);
            overflow: hidden;
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            margin-bottom: 16px;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 状态栏 */
        .ios-device-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 15px;
            font-weight: 600;
            background-color: var(--ios-systemBackground);
            position: sticky;
            top: 0;
            z-index: 40;
        }
        
        /* 导航栏 */
        .ios-nav-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            position: sticky;
            top: 44px;
            z-index: 35;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-nav-back {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
            display: flex;
            align-items: center;
        }
        
        .ios-nav-title {
            font-size: 17px;
            font-weight: 600;
            color: var(--ios-label);
        }
        
        /* iOS底部操作栏 */
        .ios-action-bar {
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            padding: 12px 16px calc(12px + env(safe-area-inset-bottom));
            border-top: 0.5px solid var(--ios-separator);
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 30;
        }

        /* iOS主按钮 */
        .ios-primary-button {
            background-color: var(--ios-blue);
            color: white;
            border-radius: var(--ios-corner-radius-large);
            font-size: 17px;
            font-weight: 600;
            padding: 12px 20px;
            width: 100%;
            text-align: center;
            transition: all 0.15s ease;
        }
        
        .ios-primary-button:active {
            transform: scale(0.97);
            background-color: rgba(0, 122, 255, 0.8);
        }

        /* iOS分类选择器 */
        .ios-category-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }
        
        .ios-category-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 10px;
            border-radius: var(--ios-corner-radius-medium);
            border: 1px solid var(--ios-separator);
            text-align: center;
            transition: all 0.2s ease;
        }
        
        .ios-category-option.selected {
            border-color: var(--ios-blue);
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--ios-blue);
        }
        
        .ios-category-icon {
            font-size: 20px;
            margin-bottom: 8px;
        }

        /* iOS表单标签 */
        .ios-form-label {
            font-size: 15px;
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--ios-label);
        }

        /* iOS表单控件 */
        .ios-input {
            display: block;
            width: 100%;
            padding: 12px 16px;
            border-radius: var(--ios-corner-radius-medium);
            border: 1px solid var(--ios-separator);
            background-color: var(--ios-systemBackground);
            font-size: 17px;
            color: var(--ios-label);
            -webkit-appearance: none;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }
        
        .ios-input:focus {
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 1px var(--ios-blue);
            outline: none;
        }

        /* iOS下拉框 */
        .ios-select {
            display: block;
            width: 100%;
            padding: 12px 16px;
            border-radius: var(--ios-corner-radius-medium);
            border: 1px solid var(--ios-separator);
            background-color: var(--ios-systemBackground);
            font-size: 17px;
            color: var(--ios-label);
            -webkit-appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%238E8E93'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 20px;
            padding-right: 40px;
        }

        /* iOS文本域 */
        .ios-textarea {
            display: block;
            width: 100%;
            padding: 12px 16px;
            border-radius: var(--ios-corner-radius-medium);
            border: 1px solid var(--ios-separator);
            background-color: var(--ios-systemBackground);
            font-size: 17px;
            color: var(--ios-label);
            resize: none;
            min-height: 120px;
            -webkit-appearance: none;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }
        
        .ios-textarea:focus {
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 1px var(--ios-blue);
            outline: none;
        }
        
        /* 图片上传区域 */
        .ios-upload-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }
        
        .ios-upload-box {
            aspect-ratio: 1;
            border: 1px dashed var(--ios-separator);
            border-radius: var(--ios-corner-radius-medium);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: var(--ios-secondarySystemBackground);
            transition: all 0.2s ease;
        }
        
        .ios-upload-box:active {
            background-color: rgba(0, 122, 255, 0.05);
            border-color: var(--ios-blue);
        }

        /* iOS列表项 */
        .ios-list-item {
            padding: 12px 0;
            display: flex;
            align-items: center;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-list-item:last-child {
            border-bottom: none;
        }
        
        .ios-list-icon {
            width: 24px;
            color: var(--ios-secondaryLabel);
            display: flex;
            justify-content: center;
            margin-right: 12px;
        }
        
        /* iOS触感反馈 */
        .ios-haptic {
            cursor: pointer;
        }

        /* iOS分隔线 */
        .ios-separator {
            height: 0.5px;
            background-color: var(--ios-separator);
            width: 100%;
        }
        
        /* iOS规格项 */
        .ios-spec-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(8px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in-delay-1 { animation-delay: 0.05s; }
        .ios-fade-in-delay-2 { animation-delay: 0.1s; }
        .ios-fade-in-delay-3 { animation-delay: 0.15s; }
        .ios-fade-in-delay-4 { animation-delay: 0.2s; }
        
        .category-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .category-scroll {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen">
            <div class="screen-title">发布农产品</div>
            
            <!-- 设备状态栏 -->
            <div class="ios-device-status-bar">
                <div>9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="ios-nav-bar">
                <button onclick="history.back()" class="ios-button ios-haptic ios-nav-back">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <div class="ios-nav-title">发布农产品</div>
                <div class="w-10"></div>
            </div>

            <!-- 发布表单 -->
            <div class="p-4 pb-28 ios-fade-in ios-fade-in-delay-1">
                <!-- 选择分类 -->
                <div class="ios-card p-4">
                    <div class="space-y-8">
                        <div>
                            <label class="ios-form-label text-[15px] font-medium text-[var(--ios-secondaryLabel)] mb-4">产品分类</label>
                            <div class="overflow-x-auto category-scroll -mx-4 px-4">
                                <div class="grid grid-cols-4 gap-2.5 w-full">
                                    <button type="button" class="ios-category-option selected ios-button ios-haptic h-[72px] flex flex-col items-center justify-center rounded-xl">
                                        <i class="fas fa-drumstick-bite text-xl mb-1.5"></i>
                                        <span class="text-[13px] whitespace-nowrap">家禽家畜</span>
                                    </button>
                                    <button type="button" class="ios-category-option ios-button ios-haptic h-[72px] flex flex-col items-center justify-center rounded-xl">
                                        <i class="fas fa-fish text-xl mb-1.5"></i>
                                        <span class="text-[13px] whitespace-nowrap">鱼虾水产</span>
                                    </button>
                                    <button type="button" class="ios-category-option ios-button ios-haptic h-[72px] flex flex-col items-center justify-center rounded-xl">
                                        <i class="fas fa-carrot text-xl mb-1.5"></i>
                                        <span class="text-[13px] whitespace-nowrap">蔬菜瓜果</span>
                                    </button>
                                    <button type="button" class="ios-category-option ios-button ios-haptic h-[72px] flex flex-col items-center justify-center rounded-xl">
                                        <i class="fas fa-egg text-xl mb-1.5"></i>
                                        <span class="text-[13px] whitespace-nowrap">禽蛋</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <label class="ios-form-label text-[15px] font-medium text-[var(--ios-secondaryLabel)] mb-4">二级分类</label>
                            <div class="grid grid-cols-4 gap-2.5">
                                <button type="button" class="ios-category-option selected ios-button ios-haptic h-9 flex items-center justify-center rounded-full">
                                    <span class="text-[14px] font-medium whitespace-nowrap">鸡</span>
                                </button>
                                <button type="button" class="ios-category-option ios-button ios-haptic h-9 flex items-center justify-center rounded-full">
                                    <span class="text-[14px] font-medium whitespace-nowrap">鸭</span>
                                </button>
                                <button type="button" class="ios-category-option ios-button ios-haptic h-9 flex items-center justify-center rounded-full">
                                    <span class="text-[14px] font-medium whitespace-nowrap">鹅</span>
                                </button>
                                <button type="button" class="ios-category-option ios-button ios-haptic h-9 flex items-center justify-center rounded-full">
                                    <span class="text-[14px] font-medium whitespace-nowrap">其他</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 产品名称 -->
                <div class="ios-card p-4 ios-fade-in ios-fade-in-delay-2">
                    <label for="product-name" class="ios-form-label">产品名称</label>
                    <input type="text" id="product-name" placeholder="请输入产品名称，例如：散养土鸡" class="ios-input ios-haptic" value="">
                </div>

                <!-- 产品图片 -->
                <div class="ios-card p-4 ios-fade-in ios-fade-in-delay-2">
                    <label class="ios-form-label">产品图片（建议上传3-5张清晰图片）</label>
                    <div class="ios-upload-grid">
                        <div class="ios-upload-box ios-button ios-haptic">
                            <i class="fas fa-camera text-[var(--ios-secondaryLabel)] text-xl mb-1"></i>
                            <span class="text-xs text-[var(--ios-secondaryLabel)]">点击上传</span>
                        </div>
                        <div class="ios-upload-box ios-button ios-haptic">
                            <i class="fas fa-plus text-[var(--ios-secondaryLabel)]"></i>
                        </div>
                        <div class="ios-upload-box ios-button ios-haptic">
                            <i class="fas fa-plus text-[var(--ios-secondaryLabel)]"></i>
                        </div>
                        <div class="ios-upload-box ios-button ios-haptic">
                            <i class="fas fa-plus text-[var(--ios-secondaryLabel)]"></i>
                        </div>
                    </div>
                </div>

                <!-- 产品价格 -->
                <div class="ios-card p-4 ios-fade-in ios-fade-in-delay-3">
                    <label for="product-price" class="ios-form-label">产品价格</label>
                    <div class="flex items-center">
                        <span class="text-[var(--ios-red)] font-medium mr-2">¥</span>
                        <input type="number" id="product-price" placeholder="请输入价格" class="ios-input ios-haptic flex-1">
                        <div class="ml-3">
                            <select class="ios-select ios-haptic w-24">
                                <option>/只</option>
                                <option>/斤</option>
                                <option>/公斤</option>
                                <option>/份</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 产品规格 -->
                    <label class="ios-form-label mt-4">产品规格</label>
                    <div class="border rounded-lg p-4 space-y-3">
                        <div class="ios-spec-item">
                            <div class="flex items-center">
                                <input type="checkbox" checked class="mr-2 h-4 w-4 ios-haptic accent-[var(--ios-blue)]">
                                <input type="text" placeholder="规格名称，如：整鸡" class="border-none p-0 text-sm outline-none" value="整鸡（约2斤）">
                            </div>
                            <div class="flex items-center">
                                <span class="mr-1 text-sm">¥</span>
                                <input type="text" placeholder="价格" class="border-none p-0 text-sm w-12 outline-none text-right" value="38.8">
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <button type="button" class="flex items-center text-[var(--ios-blue)] text-sm ios-button ios-haptic">
                                <i class="fas fa-plus mr-1"></i>
                                添加规格
                            </button>
                            <span class="text-xs text-[var(--ios-secondaryLabel)]">可添加多个规格</span>
                        </div>
                    </div>
                </div>

                <!-- 库存数量 -->
                <div class="ios-card p-4 ios-fade-in ios-fade-in-delay-3">
                    <label for="product-stock" class="ios-form-label">库存数量</label>
                    <div class="flex items-center">
                        <input type="number" id="product-stock" placeholder="请输入库存数量" class="ios-input ios-haptic flex-1">
                        <span class="ml-3 text-[var(--ios-secondaryLabel)]">只</span>
                    </div>
                </div>

                <!-- 产品详情 -->
                <div class="ios-card p-4 ios-fade-in ios-fade-in-delay-4">
                    <label for="product-detail" class="ios-form-label">产品详情</label>
                    <textarea id="product-detail" rows="5" placeholder="请详细描述您的产品特点，如：养殖方式、饲养周期、饲料类型、产品特点等" class="ios-textarea ios-haptic">我家土鸡采用散养方式，在自然环境中自由觅食，饲养周期180天以上，不使用任何生长激素，肉质紧实，营养丰富，适合炖汤。</textarea>
                </div>

                <!-- 配送信息 -->
                <div class="ios-card p-4 ios-fade-in ios-fade-in-delay-4">
                    <label class="ios-form-label">配送信息</label>
                    <div class="border rounded-lg p-4 space-y-3">
                        <div class="ios-list-item">
                            <i class="fas fa-truck ios-list-icon"></i>
                            <span class="text-sm mr-3">配送方式</span>
                            <select class="flex-1 text-sm border-none outline-none ios-haptic bg-transparent">
                                <option>自行配送</option>
                                <option>顾客自提</option>
                                <option>快递配送</option>
                            </select>
                        </div>
                        <div class="ios-list-item">
                            <i class="fas fa-map-marker-alt ios-list-icon"></i>
                            <span class="text-sm mr-3">配送范围</span>
                            <select class="flex-1 text-sm border-none outline-none ios-haptic bg-transparent">
                                <option>本小区内</option>
                                <option>3公里内</option>
                                <option>5公里内</option>
                                <option>10公里内</option>
                            </select>
                        </div>
                        <div class="ios-list-item">
                            <i class="fas fa-money-bill-wave ios-list-icon"></i>
                            <span class="text-sm mr-3">配送费用</span>
                            <select class="flex-1 text-sm border-none outline-none ios-haptic bg-transparent">
                                <option>免费配送</option>
                                <option>5元起</option>
                                <option>按距离收费</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部发布按钮 -->
            <div class="ios-action-bar">
                <button type="button" id="publishBtn" class="ios-primary-button ios-button ios-haptic">
                    发布产品
                </button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 分类按钮点击事件
            const categoryBtns = document.querySelectorAll('.ios-category-option');
            categoryBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 添加稍强的触感反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate([5, 15]);
                    }
                    
                    // 找到同级别的所有按钮
                    const parentGrid = this.closest('.ios-category-grid');
                    const siblingBtns = parentGrid.querySelectorAll('.ios-category-option');
                    
                    // 重置所有按钮样式
                    siblingBtns.forEach(sibling => {
                        sibling.classList.remove('selected');
                    });
                    
                    // 设置当前按钮样式
                    this.classList.add('selected');
                });
            });
            
            // 表单控件焦点效果
            const formControls = document.querySelectorAll('.ios-input, .ios-textarea, .ios-select');
            formControls.forEach(control => {
                control.addEventListener('focus', function() {
                    // 轻微触感反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate(5);
                    }
                });
            });
            
            // 上传图片框点击效果
            const uploadBoxes = document.querySelectorAll('.ios-upload-box');
            uploadBoxes.forEach(box => {
                box.addEventListener('click', function() {
                    // 轻微触感反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate([5, 10]);
                    }
                });
            });
            
            // 发布按钮点击事件
            document.getElementById('publishBtn').addEventListener('click', function() {
                // 强烈的触感反馈
                if ('vibrate' in navigator) {
                    navigator.vibrate([10, 30, 10]);
                }
                
                alert('发布成功！您的农产品信息已提交，等待审核。');
                window.location.href = 'market-home.html';
            });
        });
    </script>
</body>
</html> 