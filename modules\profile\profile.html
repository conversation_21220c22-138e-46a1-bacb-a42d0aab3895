<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 个人中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow */
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .ios-card:active {
            transform: scale(0.98);
            box-shadow: 0 1px 3px rgba(0,0,0,0.02);
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            padding-bottom: max(env(safe-area-inset-bottom), 16px);
            border-top: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 600;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS标题文字样式 */
        .ios-section-title {
            font-weight: 600;
            letter-spacing: -0.01em;
            color: var(--ios-label);
            font-size: 20px;
        }
        
        /* iOS标签样式 */
        .ios-tag {
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
            padding: 2px 8px;
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
            transition: all 0.2s ease;
        }
        
        /* iOS列表分割线样式 */
        .ios-separator {
            height: 0.5px;
            background-color: rgba(60,60,67,0.1);
            margin-left: 16px;
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }
        
        /* iOS列表项样式 */
        .ios-list-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 14px 16px;
            background-color: var(--ios-systemBackground);
            position: relative;
            transition: background-color 0.2s ease;
        }
        
        .ios-list-item:active {
            background-color: rgba(0,0,0,0.04);
        }
        
        .ios-list-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            margin-right: 12px;
            font-size: 14px;
            color: white;
        }
        
        /* 平滑滚动 */
        .smooth-scroll {
            scroll-behavior: smooth;
        }
        
        /* 用户徽章样式 */
        .ios-badge {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.15s ease;
        }
        
        /* iOS统计数据项样式 */
        .ios-stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 0;
            position: relative;
        }
        
        .ios-stat-item:after {
            content: '';
            height: 24px;
            width: 0.5px;
            background-color: rgba(60,60,67,0.1);
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .ios-stat-item:last-child:after {
            display: none;
        }
        
        .ios-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            object-fit: cover;
            border: 1px solid rgba(0,0,0,0.05);
            background-color: #fff;
            box-shadow: 0 1px 5px rgba(0,0,0,0.05);
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator">
            <div class="screen-title">个人中心</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <h1 class="text-center font-semibold">个人中心</h1>
                <div class="flex space-x-4">
                    <button class="ios-button ios-haptic text-[#007AFF]">
                        <i class="fas fa-qrcode text-xl"></i>
                    </button>
                    <button class="ios-button ios-haptic text-[#007AFF]" onclick="window.location.href='settings.html'">
                        <i class="fas fa-cog text-xl"></i>
                    </button>
                </div>
            </div>

            <div class="pb-32">
                <!-- 用户信息卡片 -->
                <div class="bg-white p-4 ios-fade-in">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde" class="ios-avatar">
                        <div class="ml-4 flex-1">
                            <div class="flex items-center">
                                <h2 class="text-lg font-semibold">张三</h2>
                                <!-- 业主认证徽章 -->
                                <div class="ml-2 ios-badge bg-[rgba(255,149,0,0.1)] border border-[rgba(255,149,0,0.2)]">
                                    <i class="fas fa-home text-[#FF9500] mr-1"></i>
                                    <span class="text-[#FF9500]">业主</span>
                                </div>
                            </div>
                            <p class="text-sm text-[var(--ios-secondaryLabel)] mt-1">ID: 888888</p>
                            <!-- 当前小区信息 -->
                            <div class="flex items-center mt-1">
                                <i class="fas fa-building text-[#007AFF] text-xs mr-1"></i>
                                <span class="text-sm text-[#007AFF]">春题·杭玥府</span>
                            </div>
                            
                            <!-- 个人简介 -->
                            <p class="text-sm text-[var(--ios-secondaryLabel)] mt-2 line-clamp-1">点击编辑个人资料，完善更多信息</p>
                            
                            <!-- 编辑资料按钮 -->
                            <button class="mt-2 text-sm text-[#007AFF] ios-button ios-haptic" onclick="window.location.href='my-info.html'">
                                编辑资料
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 数据统计 -->
                <div class="bg-white mt-2 grid grid-cols-4 ios-fade-in" style="animation-delay: 0.1s;">
                    <div class="ios-stat-item ios-button ios-haptic" onclick="window.location.href='my-posts.html'">
                        <div class="text-lg font-semibold">8</div>
                        <div class="text-sm text-[var(--ios-secondaryLabel)]">发布</div>
                    </div>
                    <div class="ios-stat-item ios-button ios-haptic" onclick="window.location.href='favorites.html'">
                        <div class="text-lg font-semibold">12</div>
                        <div class="text-sm text-[var(--ios-secondaryLabel)]">收藏</div>
                    </div>
                    <div class="ios-stat-item ios-button ios-haptic">
                        <div class="text-lg font-semibold">25</div>
                        <div class="text-sm text-[var(--ios-secondaryLabel)]">关注</div>
                    </div>
                    <div class="ios-stat-item ios-button ios-haptic">
                        <div class="text-lg font-semibold">18</div>
                        <div class="text-sm text-[var(--ios-secondaryLabel)]">粉丝</div>
                    </div>
                </div>

                <!-- 功能列表 -->
                <div class="mt-5 ios-fade-in" style="animation-delay: 0.15s;">
                    <h3 class="px-4 py-2 text-sm font-medium text-[var(--ios-secondaryLabel)]">账号与安全</h3>
                    <div class="bg-white rounded-t-xl overflow-hidden">
                        <!-- 用户认证入口 -->
                        <div class="ios-list-item ios-haptic" onclick="window.location.href='verification-status.html'">
                            <div class="flex items-center">
                                <div class="ios-list-icon bg-blue-500">
                                    <i class="fas fa-id-badge"></i>
                                </div>
                                <span>我的认证</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm text-green-500 mr-2">已认证</span>
                                <i class="fas fa-chevron-right text-[var(--ios-tertiaryLabel)]"></i>
                            </div>
                        </div>
                        <div class="ios-separator"></div>
                        
                        <!-- 我的小区入口 -->
                        <div class="ios-list-item ios-haptic" onclick="window.location.href='my-communities.html'">
                            <div class="flex items-center">
                                <div class="ios-list-icon bg-purple-500">
                                    <i class="fas fa-building"></i>
                                </div>
                                <span>我的小区</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm text-[var(--ios-secondaryLabel)] mr-2">3个小区</span>
                                <i class="fas fa-chevron-right text-[var(--ios-tertiaryLabel)]"></i>
                            </div>
                        </div>
                        <div class="ios-separator"></div>
                        
                        <div class="ios-list-item ios-haptic" onclick="window.location.href='security.html'">
                            <div class="flex items-center">
                                <div class="ios-list-icon bg-yellow-500">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <span>账号安全</span>
                            </div>
                            <i class="fas fa-chevron-right text-[var(--ios-tertiaryLabel)]"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 我的内容 -->
                <div class="mt-5 ios-fade-in" style="animation-delay: 0.2s;">
                    <h3 class="px-4 py-2 text-sm font-medium text-[var(--ios-secondaryLabel)]">我的内容</h3>
                    <div class="bg-white rounded-xl overflow-hidden">
                        <div class="ios-list-item ios-haptic" onclick="window.location.href='my-posts.html'">
                            <div class="flex items-center">
                                <div class="ios-list-icon bg-blue-500">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <span>我的发布</span>
                            </div>
                            <i class="fas fa-chevron-right text-[var(--ios-tertiaryLabel)]"></i>
                        </div>
                        <div class="ios-separator"></div>
                        
                        <div class="ios-list-item ios-haptic" onclick="window.location.href='my-favorites.html'">
                            <div class="flex items-center">
                                <div class="ios-list-icon bg-red-500">
                                    <i class="fas fa-heart"></i>
                                </div>
                                <span>我的收藏</span>
                            </div>
                            <i class="fas fa-chevron-right text-[var(--ios-tertiaryLabel)]"></i>
                        </div>
                        <div class="ios-separator"></div>
                        
                        <div class="ios-list-item ios-haptic" onclick="window.location.href='my-history.html'">
                            <div class="flex items-center">
                                <div class="ios-list-icon bg-green-500">
                                    <i class="fas fa-history"></i>
                                </div>
                                <span>浏览历史</span>
                            </div>
                            <i class="fas fa-chevron-right text-[var(--ios-tertiaryLabel)]"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 通知与设置 -->
                <div class="mt-5 mb-5 ios-fade-in" style="animation-delay: 0.25s;">
                    <h3 class="px-4 py-2 text-sm font-medium text-[var(--ios-secondaryLabel)]">系统与服务</h3>
                    <div class="bg-white rounded-xl overflow-hidden">
                        <div class="ios-list-item ios-haptic" onclick="window.location.href='notifications.html'">
                            <div class="flex items-center">
                                <div class="ios-list-icon bg-purple-500">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <span>消息通知</span>
                            </div>
                            <i class="fas fa-chevron-right text-[var(--ios-tertiaryLabel)]"></i>
                        </div>
                        <div class="ios-separator"></div>
                        
                        <div class="ios-list-item ios-haptic" onclick="window.location.href='settings.html'">
                            <div class="flex items-center">
                                <div class="ios-list-icon bg-gray-500">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <span>设置</span>
                            </div>
                            <i class="fas fa-chevron-right text-[var(--ios-tertiaryLabel)]"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="fixed bottom-0 left-0 right-0 ios-bottom-nav flex justify-around pt-2">
                <button class="flex flex-col items-center px-4 ios-button ios-haptic" onclick="window.location.href='../navigation/home.html'">
                    <i class="fas fa-home text-gray-400 text-xl"></i>
                    <span class="text-xs text-gray-400 mt-1">首页</span>
                </button>
                <button class="flex flex-col items-center px-4 ios-button ios-haptic" onclick="window.location.href='../navigation/discover.html'">
                    <i class="fas fa-compass text-gray-400 text-xl"></i>
                    <span class="text-xs text-gray-400 mt-1">发现</span>
                </button>
                <button class="flex flex-col items-center px-4 ios-button ios-haptic">
                    <div class="w-12 h-12 bg-[#007AFF] rounded-full flex items-center justify-center -mt-4 shadow-md">
                        <i class="fas fa-plus text-white text-xl"></i>
                    </div>
                </button>
                <button class="flex flex-col items-center px-4 ios-button ios-haptic" onclick="window.location.href='../navigation/messages.html'">
                    <i class="fas fa-comment text-gray-400 text-xl"></i>
                    <span class="text-xs text-gray-400 mt-1">消息</span>
                </button>
                <button class="flex flex-col items-center px-4 ios-button ios-haptic">
                    <i class="fas fa-user text-[#007AFF] text-xl"></i>
                    <span class="text-xs text-[#007AFF] mt-1">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 示例：获取认证状态（实际应用中应从后端获取）
            const isVerified = true; // 假设已认证
            const verificationRole = 'owner'; // 假设是业主角色
            
            // 根据认证状态和角色调整UI
            const verificationStatusText = document.querySelector('.text-green-500');
            const userBadge = document.querySelector('.ios-badge');
            
            if (!isVerified) {
                verificationStatusText.textContent = '未认证';
                verificationStatusText.classList.remove('text-green-500');
                verificationStatusText.classList.add('text-[var(--ios-secondaryLabel)]');
                userBadge.classList.add('hidden');
            } else {
                // 已认证，根据角色显示不同徽章
                if (verificationRole === 'owner') {
                    // 业主徽章已在HTML中设置
                } else if (verificationRole === 'tenant') {
                    const roleBadgeIcon = userBadge.querySelector('i');
                    const roleBadgeText = userBadge.querySelector('span');
                    
                    roleBadgeIcon.classList.remove('fa-home');
                    roleBadgeIcon.classList.add('fa-key');
                    roleBadgeText.textContent = '租户';
                    
                    userBadge.classList.remove('bg-[rgba(255,149,0,0.1)]', 'border-[rgba(255,149,0,0.2)]');
                    userBadge.classList.add('bg-[rgba(0,122,255,0.1)]', 'border-[rgba(0,122,255,0.2)]');
                    roleBadgeIcon.classList.remove('text-[#FF9500]');
                    roleBadgeIcon.classList.add('text-[#007AFF]');
                    roleBadgeText.classList.remove('text-[#FF9500]');
                    roleBadgeText.classList.add('text-[#007AFF]');
                } else if (verificationRole === 'property') {
                    const roleBadgeIcon = userBadge.querySelector('i');
                    const roleBadgeText = userBadge.querySelector('span');
                    
                    roleBadgeIcon.classList.remove('fa-home');
                    roleBadgeIcon.classList.add('fa-building');
                    roleBadgeText.textContent = '物业';
                    
                    userBadge.classList.remove('bg-[rgba(255,149,0,0.1)]', 'border-[rgba(255,149,0,0.2)]');
                    userBadge.classList.add('bg-[rgba(88,86,214,0.1)]', 'border-[rgba(88,86,214,0.2)]');
                    roleBadgeIcon.classList.remove('text-[#FF9500]');
                    roleBadgeIcon.classList.add('text-[#5856D6]');
                    roleBadgeText.classList.remove('text-[#FF9500]');
                    roleBadgeText.classList.add('text-[#5856D6]');
                }
            }
            
            // 认证入口点击处理
            const certificationEntry = document.querySelector('[onclick="window.location.href=\'verification-status.html\'"]');
            certificationEntry.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // 根据认证状态跳转到不同页面
                if (!isVerified) {
                    window.location.href = 'user-verification.html'; // 未认证时跳转到认证页面
                } else {
                    window.location.href = 'verification-status.html'; // 已认证时查看认证状态
                }
            });
            
            // 模拟未认证状态（仅用于演示）
            const toggleVerificationStatus = () => {
                // 添加一个隐藏的开关，便于演示
                const url = new URL(window.location.href);
                if (url.searchParams.has('unverified')) {
                    verificationStatusText.textContent = '未认证';
                    verificationStatusText.classList.remove('text-green-500');
                    verificationStatusText.classList.add('text-[var(--ios-secondaryLabel)]');
                    userBadge.classList.add('hidden');
                }
            };
            
            toggleVerificationStatus();
        });
    </script>
</body>
</html> 