<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 用户认证</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            padding-bottom: max(env(safe-area-inset-bottom), 16px);
            border-top: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 600;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* iOS标签样式 */
        .ios-tag {
            font-size: 13px;
            font-weight: 500;
            border-radius: 8px;
            padding: 12px;
            transition: all 0.2s ease;
        }
        
        /* iOS表单元素 */
        .ios-form-group {
            margin-bottom: 20px;
        }
        
        .ios-label {
            font-size: 15px;
            font-weight: 500;
            color: var(--ios-label);
            margin-bottom: 8px;
            display: block;
        }
        
        .ios-input {
            width: 100%;
            background-color: #fff;
            border: 0.5px solid rgba(0,0,0,0.1);
            border-radius: 10px;
            padding: 12px 16px;
            font-size: 15px;
            color: var(--ios-label);
            transition: all 0.2s ease;
        }
        
        .ios-input:focus {
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 1px rgba(0,122,255,0.2);
            outline: none;
        }
        
        .ios-input::placeholder {
            color: var(--ios-tertiaryLabel);
        }
        
        .ios-select {
            width: 100%;
            background-color: #fff;
            border: 0.5px solid rgba(0,0,0,0.1);
            border-radius: 10px;
            padding: 12px 16px;
            font-size: 15px;
            color: var(--ios-label);
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
        }
        
        .ios-select:focus {
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 1px rgba(0,122,255,0.2);
            outline: none;
        }
        
        /* iOS固定按钮 */
        .ios-fixed-btn {
            background-color: var(--ios-blue);
            color: white;
            border-radius: 12px;
            font-weight: 500;
            font-size: 16px;
            padding: 14px 0;
            width: 100%;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,122,255,0.3);
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), box-shadow 0.2s ease;
        }
        
        .ios-fixed-btn:active {
            transform: scale(0.98);
            box-shadow: 0 1px 4px rgba(0,122,255,0.2);
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }
        
        /* iOS上传按钮 */
        .ios-upload-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(0,0,0,0.03);
            border-radius: 8px;
            border: 1px dashed rgba(0,0,0,0.1);
            padding: 24px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .ios-upload-btn:hover {
            background-color: rgba(0,0,0,0.05);
        }
        
        /* 平滑滚动 */
        .smooth-scroll {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator">
            <div class="screen-title">用户认证</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="history.back()" class="ios-button ios-haptic text-[#007AFF]">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <h1 class="text-center font-semibold">用户认证</h1>
                <div></div>
            </div>

            <div class="pb-32">
                <!-- 认证说明 -->
                <div class="bg-[rgba(0,122,255,0.08)] p-4 m-4 rounded-xl ios-fade-in">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-[#007AFF] mr-2"></i>
                        <p class="text-sm text-[#007AFF]">完成认证可获得相应身份标识，并解锁更多功能与权益。</p>
                    </div>
                </div>

                <!-- 认证表单 -->
                <form class="px-4 space-y-6 ios-fade-in" style="animation-delay: 0.1s;">
                    <!-- 认证类型选择 -->
                    <div class="ios-form-group">
                        <label class="ios-label">选择认证类型</label>
                        <div class="grid grid-cols-3 gap-3">
                            <button type="button" id="owner-btn" class="ios-tag bg-white border border-[rgba(0,0,0,0.1)] text-[var(--ios-secondaryLabel)] flex flex-col items-center ios-button ios-haptic">
                                <i class="fas fa-home text-lg mb-1"></i>
                                <span class="text-sm">业主认证</span>
                            </button>
                            <button type="button" id="tenant-btn" class="ios-tag bg-white border border-[rgba(0,0,0,0.1)] text-[var(--ios-secondaryLabel)] flex flex-col items-center ios-button ios-haptic">
                                <i class="fas fa-key text-lg mb-1"></i>
                                <span class="text-sm">租户认证</span>
                            </button>
                            <button type="button" id="property-btn" class="ios-tag bg-white border border-[rgba(0,0,0,0.1)] text-[var(--ios-secondaryLabel)] flex flex-col items-center ios-button ios-haptic">
                                <i class="fas fa-building text-lg mb-1"></i>
                                <span class="text-sm">物业认证</span>
                            </button>
                        </div>
                    </div>

                    <!-- 真实姓名 -->
                    <div class="ios-form-group">
                        <label class="ios-label">真实姓名</label>
                        <input type="text" placeholder="请输入您的真实姓名" class="ios-input">
                    </div>

                    <!-- 身份证号 -->
                    <div class="ios-form-group">
                        <label class="ios-label">身份证号</label>
                        <input type="text" placeholder="请输入您的身份证号" class="ios-input">
                    </div>

                    <!-- 房屋信息 - 业主认证时显示 -->
                    <div id="owner-fields" class="space-y-4 hidden">
                        <div class="ios-form-group">
                            <label class="ios-label">所在小区</label>
                            <select class="ios-select">
                                <option>春题·杭玥府</option>
                                <option>桃源居</option>
                                <option>翡翠城</option>
                                <option>金色家园</option>
                            </select>
                        </div>
                        
                        <div class="ios-form-group">
                            <label class="ios-label">房屋信息</label>
                            <div class="flex gap-4">
                                <input type="text" placeholder="楼栋单元，如3栋2单元" class="ios-input flex-1">
                                <input type="text" placeholder="房间号，如1201" class="ios-input flex-1">
                            </div>
                        </div>
                    </div>

                    <!-- 租住信息 - 租户认证时显示 -->
                    <div id="tenant-fields" class="space-y-4 hidden">
                        <div class="ios-form-group">
                            <label class="ios-label">租住小区</label>
                            <select class="ios-select">
                                <option>春题·杭玥府</option>
                                <option>桃源居</option>
                                <option>翡翠城</option>
                                <option>金色家园</option>
                            </select>
                        </div>
                        
                        <div class="ios-form-group">
                            <label class="ios-label">租住地址</label>
                            <div class="flex gap-4">
                                <input type="text" placeholder="楼栋单元，如3栋2单元" class="ios-input flex-1">
                                <input type="text" placeholder="房间号，如1201" class="ios-input flex-1">
                            </div>
                        </div>
                        
                        <div class="ios-form-group">
                            <label class="ios-label">租期</label>
                            <div class="flex gap-4">
                                <input type="date" placeholder="开始日期" class="ios-input flex-1">
                                <input type="date" placeholder="结束日期" class="ios-input flex-1">
                            </div>
                        </div>
                    </div>

                    <!-- 物业信息 - 物业认证时显示 -->
                    <div id="property-fields" class="space-y-4 hidden">
                        <div class="ios-form-group">
                            <label class="ios-label">物业公司</label>
                            <input type="text" placeholder="请输入物业公司名称" class="ios-input">
                        </div>
                        
                        <div class="ios-form-group">
                            <label class="ios-label">职务</label>
                            <input type="text" placeholder="请输入您的职务" class="ios-input">
                        </div>
                        
                        <div class="ios-form-group">
                            <label class="ios-label">工号</label>
                            <input type="text" placeholder="请输入您的工号" class="ios-input">
                        </div>
                    </div>

                    <!-- 认证材料 -->
                    <div class="ios-form-group">
                        <label class="ios-label">上传认证材料</label>
                        <div class="grid grid-cols-4 gap-3">
                            <div class="aspect-square ios-upload-btn ios-haptic">
                                <i class="fas fa-plus text-[var(--ios-tertiaryLabel)]"></i>
                            </div>
                            <div class="aspect-square ios-upload-btn ios-haptic">
                                <i class="fas fa-plus text-[var(--ios-tertiaryLabel)]"></i>
                            </div>
                        </div>
                        <p class="text-xs text-[var(--ios-secondaryLabel)] mt-2" id="upload-tip">请上传相关证明材料照片（身份证、房产证等）</p>
                    </div>

                    <!-- 手机验证 -->
                    <div class="ios-form-group">
                        <label class="ios-label">手机验证</label>
                        <div class="flex items-center gap-3">
                            <input type="tel" placeholder="请输入您的手机号" class="ios-input flex-1">
                            <button type="button" class="whitespace-nowrap px-4 py-3 bg-[#007AFF] text-white rounded-lg ios-button ios-haptic">获取验证码</button>
                        </div>
                    </div>
                    
                    <div class="ios-form-group">
                        <label class="ios-label">验证码</label>
                        <input type="text" placeholder="请输入短信验证码" class="ios-input">
                    </div>
                </form>
            </div>

            <!-- 底部按钮 -->
            <div class="fixed bottom-0 left-0 right-0 p-4 ios-bottom-nav">
                <button id="submit-btn" class="ios-fixed-btn ios-button ios-haptic">
                    提交认证
                </button>
                
                <!-- 快速演示链接（仅用于演示） -->
                <div class="mt-4">
                    <p class="text-xs text-[var(--ios-secondaryLabel)] text-center mb-2">快速演示（跳过表单验证）：</p>
                    <div class="flex justify-around gap-3">
                        <a href="verification-status.html?status=pending" class="bg-[rgba(0,0,0,0.04)] text-[var(--ios-secondaryLabel)] py-1.5 px-3 rounded-lg text-xs flex-1 text-center ios-button ios-haptic">提交审核中</a>
                        <a href="verification-status.html" class="bg-[rgba(0,0,0,0.04)] text-[var(--ios-secondaryLabel)] py-1.5 px-3 rounded-lg text-xs flex-1 text-center ios-button ios-haptic">直接认证成功</a>
                        <a href="verification-status.html?status=failed" class="bg-[rgba(0,0,0,0.04)] text-[var(--ios-secondaryLabel)] py-1.5 px-3 rounded-lg text-xs flex-1 text-center ios-button ios-haptic">审核失败</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 认证类型按钮
            const ownerBtn = document.getElementById('owner-btn');
            const tenantBtn = document.getElementById('tenant-btn');
            const propertyBtn = document.getElementById('property-btn');
            
            // 对应的字段组
            const ownerFields = document.getElementById('owner-fields');
            const tenantFields = document.getElementById('tenant-fields');
            const propertyFields = document.getElementById('property-fields');
            
            // 上传提示
            const uploadTip = document.getElementById('upload-tip');
            
            // 选择按钮点击事件
            ownerBtn.addEventListener('click', function() {
                resetButtons();
                this.classList.remove('text-[var(--ios-secondaryLabel)]', 'bg-white', 'border-[rgba(0,0,0,0.1)]');
                this.classList.add('text-[#007AFF]', 'bg-[rgba(0,122,255,0.1)]', 'border-[#007AFF]');
                
                hideAllFields();
                ownerFields.classList.remove('hidden');
                uploadTip.textContent = '请上传身份证和房产证照片';
            });
            
            tenantBtn.addEventListener('click', function() {
                resetButtons();
                this.classList.remove('text-[var(--ios-secondaryLabel)]', 'bg-white', 'border-[rgba(0,0,0,0.1)]');
                this.classList.add('text-[#007AFF]', 'bg-[rgba(0,122,255,0.1)]', 'border-[#007AFF]');
                
                hideAllFields();
                tenantFields.classList.remove('hidden');
                uploadTip.textContent = '请上传身份证和租赁合同照片';
            });
            
            propertyBtn.addEventListener('click', function() {
                resetButtons();
                this.classList.remove('text-[var(--ios-secondaryLabel)]', 'bg-white', 'border-[rgba(0,0,0,0.1)]');
                this.classList.add('text-[#007AFF]', 'bg-[rgba(0,122,255,0.1)]', 'border-[#007AFF]');
                
                hideAllFields();
                propertyFields.classList.remove('hidden');
                uploadTip.textContent = '请上传身份证和工作证明照片';
            });
            
            // 重置所有按钮样式
            function resetButtons() {
                [ownerBtn, tenantBtn, propertyBtn].forEach(btn => {
                    btn.classList.remove('text-[#007AFF]', 'bg-[rgba(0,122,255,0.1)]', 'border-[#007AFF]');
                    btn.classList.add('text-[var(--ios-secondaryLabel)]', 'bg-white', 'border-[rgba(0,0,0,0.1)]');
                });
            }
            
            // 隐藏所有类型的字段
            function hideAllFields() {
                ownerFields.classList.add('hidden');
                tenantFields.classList.add('hidden');
                propertyFields.classList.add('hidden');
            }
            
            // 初始化选择业主认证
            ownerBtn.click();
            
            // 提交按钮点击事件
            document.getElementById('submit-btn').addEventListener('click', function() {
                // 模拟提交表单，重定向到认证状态页面
                window.location.href = 'verification-status.html?status=pending';
            });
        });
    </script>
</body>
</html> 