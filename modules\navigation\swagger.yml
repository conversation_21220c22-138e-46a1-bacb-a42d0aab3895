openapi: 3.0.3
info:
  title: 乐享友邻 - 导航与首页API
  description: 导航与首页功能后端接口文档，包含首页推荐、发现页面、消息管理等功能
  version: 1.0.0

servers:
  - url: https://dev-api.hoodly-joy.com/v1
    description: 开发环境

security:
  - BearerAuth: []

paths:
  /home/<USER>
    get:
      tags:
        - 首页管理
      summary: 获取首页仪表盘数据
      description: 获取首页展示的各模块推荐内容和统计信息
      responses:
        '200':
          description: 成功获取首页数据
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HomeDashboardResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /home/<USER>
    post:
      tags:
        - 首页管理
      summary: 获取个性化推荐内容
      description: 根据用户偏好和行为获取个性化推荐内容
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RecommendationRequest'
      responses:
        '200':
          description: 成功获取推荐内容
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecommendationResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /search/global:
    post:
      tags:
        - 搜索功能
      summary: 全局搜索
      description: 在所有模块中搜索相关内容
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GlobalSearchRequest'
      responses:
        '200':
          description: 搜索成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GlobalSearchResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /search/suggestions:
    get:
      tags:
        - 搜索功能
      summary: 获取搜索建议
      description: 获取热门搜索词和搜索建议
      parameters:
        - name: keyword
          in: query
          description: 搜索关键词前缀
          schema:
            type: string
            example: "iPhone"
        - name: limit
          in: query
          description: 返回建议数量限制
          schema:
            type: integer
            minimum: 1
            maximum: 20
            default: 10
      responses:
        '200':
          description: 成功获取搜索建议
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchSuggestionsResponse'

  /discover/feeds:
    post:
      tags:
        - 发现页面
      summary: 获取发现页面动态
      description: 获取社区动态、活动推荐等发现页面内容
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DiscoverFeedsRequest'
      responses:
        '200':
          description: 成功获取发现页面内容
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiscoverFeedsResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /discover/activities:
    post:
      tags:
        - 发现页面
      summary: 获取社区活动列表
      description: 获取当前社区的活动列表，支持分页和多维度筛选
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ActivityListRequest'
      responses:
        '200':
          description: 成功获取活动列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ActivitiesResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /messages/conversations:
    post:
      tags:
        - 消息管理
      summary: 获取会话列表
      description: 获取用户的所有会话列表，支持分页和筛选
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConversationListRequest'
      responses:
        '200':
          description: 成功获取会话列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConversationsResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /messages/conversations/{conversationId}/messages/list:
    post:
      tags:
        - 消息管理
      summary: 获取会话消息列表
      description: 获取指定会话的消息列表，支持分页和消息定位
      parameters:
        - name: conversationId
          in: path
          required: true
          description: 会话唯一标识ID
          schema:
            type: string
            example: "conversation_123456"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MessageListRequest'
      responses:
        '200':
          description: 成功获取消息列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessagesResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 会话不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /messages/conversations/{conversationId}/messages:
    post:
      tags:
        - 消息管理
      summary: 发送消息
      description: 向指定会话发送消息
      parameters:
        - name: conversationId
          in: path
          required: true
          description: 会话唯一标识ID
          schema:
            type: string
            example: "conversation_123456"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendMessageRequest'
      responses:
        '201':
          description: 消息发送成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendMessageResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 会话不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /messages/conversations/{conversationId}/read:
    put:
      tags:
        - 消息管理
      summary: 标记消息为已读
      description: 标记指定会话的消息为已读状态
      parameters:
        - name: conversationId
          in: path
          required: true
          description: 会话唯一标识ID
          schema:
            type: string
            example: "conversation_123456"
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                messageId:
                  type: string
                  description: 最后已读消息ID
                  example: "message_789012"
      responses:
        '200':
          description: 标记成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: 会话不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /notifications:
    post:
      tags:
        - 通知管理
      summary: 获取通知列表
      description: 获取用户的通知列表，支持分页和多维度筛选
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationListRequest'
      responses:
        '200':
          description: 成功获取通知列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationsResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /notifications/{notificationId}/read:
    put:
      tags:
        - 通知管理
      summary: 标记通知为已读
      description: 标记指定通知为已读状态
      parameters:
        - name: notificationId
          in: path
          required: true
          description: 通知唯一标识ID
          schema:
            type: string
            example: "notification_123456"
      responses:
        '200':
          description: 标记成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: 通知不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    HomeDashboardResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            userInfo:
              type: object
              properties:
                currentCommunity:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 小区ID
                      example: "community_123"
                    name:
                      type: string
                      description: 小区名称
                      example: "春题·杭玥府"
                    userLocation:
                      type: string
                      description: 用户位置
                      example: "5栋1单元1302"
            moduleRecommendations:
              type: object
              properties:
                secondHand:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 商品ID
                        example: "product_123"
                      title:
                        type: string
                        description: 商品标题
                        example: "iPhone 14 Pro Max"
                      price:
                        type: number
                        description: 价格
                        example: 7999
                      condition:
                        type: string
                        description: 成色
                        example: "95新"
                      image:
                        type: string
                        description: 商品图片
                        example: "https://images.example.com/phone1.jpg"
                  description: 二手闲置推荐
                parking:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 停车位ID
                        example: "parking_456"
                      title:
                        type: string
                        description: 停车位标题
                        example: "阳光花园停车位"
                      price:
                        type: number
                        description: 月租金
                        example: 300
                      features:
                        type: string
                        description: 特色描述
                        example: "24小时保安 可短租"
                      image:
                        type: string
                        description: 停车位图片
                        example: "https://images.example.com/parking1.jpg"
                  description: 停车位推荐
                housing:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 房源ID
                        example: "house_789"
                      title:
                        type: string
                        description: 房源标题
                        example: "阳光花园 2室1厅"
                      price:
                        type: number
                        description: 月租金
                        example: 3200
                      roomType:
                        type: string
                        description: 户型
                        example: "2室1厅1卫"
                      image:
                        type: string
                        description: 房源图片
                        example: "https://images.example.com/house1.jpg"
                      imageCount:
                        type: integer
                        description: 图片数量
                        example: 12
                  description: 房源租赁推荐
                market:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 农产品ID
                        example: "market_101"
                      name:
                        type: string
                        description: 商品名称
                        example: "散养土鸡"
                      price:
                        type: number
                        description: 价格
                        example: 38.8
                      unit:
                        type: string
                        description: 单位
                        example: "只"
                      farmer:
                        type: string
                        description: 农户名称
                        example: "张大叔家的农场"
                      image:
                        type: string
                        description: 商品图片
                        example: "https://images.example.com/chicken1.jpg"
                  description: 邻里集市推荐
            statistics:
              type: object
              properties:
                totalUsers:
                  type: integer
                  description: 小区总用户数
                  example: 1250
                activeToday:
                  type: integer
                  description: 今日活跃用户数
                  example: 89
                newListings:
                  type: integer
                  description: 今日新发布数
                  example: 15

    RecommendationRequest:
      type: object
      properties:
        modules:
          type: array
          items:
            type: string
            enum: [second_hand, parking, housing, market, group_buy]
          description: 需要推荐的模块
          example: ["second_hand", "parking"]
        limit:
          type: integer
          description: 每个模块返回的推荐数量
          example: 4
          minimum: 1
          maximum: 10
          default: 4
        preferences:
          type: object
          properties:
            priceRange:
              type: object
              properties:
                min:
                  type: number
                  description: 最低价格
                  example: 100
                max:
                  type: number
                  description: 最高价格
                  example: 5000
            categories:
              type: array
              items:
                type: string
              description: 偏好分类
              example: ["手机数码", "家居用品"]
      example:
        modules: ["second_hand", "parking"]
        limit: 4
        preferences:
          priceRange:
            min: 100
            max: 5000
          categories: ["手机数码", "家居用品"]

    RecommendationResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            recommendations:
              type: object
              additionalProperties:
                type: array
                items:
                  type: object
              description: 各模块推荐内容，key为模块名称
              example:
                second_hand:
                  - id: "product_123"
                    title: "iPhone 14 Pro Max"
                    price: 7999
                    condition: "95新"
                    image: "https://images.example.com/phone1.jpg"

    GlobalSearchRequest:
      type: object
      properties:
        keyword:
          type: string
          description: 搜索关键词
          example: "iPhone"
          maxLength: 100
        modules:
          type: array
          items:
            type: string
            enum: [all, second_hand, parking, housing, market, group_buy, community]
          description: 搜索范围模块
          example: ["second_hand", "housing"]
          default: ["all"]
        filters:
          type: object
          properties:
            priceRange:
              type: object
              properties:
                min:
                  type: number
                  description: 最低价格
                  example: 100
                max:
                  type: number
                  description: 最高价格
                  example: 5000
            location:
              type: object
              properties:
                communityId:
                  type: string
                  description: 小区ID
                  example: "community_123"
                radius:
                  type: number
                  description: 搜索半径（公里）
                  example: 5
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
      example:
        keyword: "iPhone"
        modules: ["second_hand", "housing"]
        filters:
          priceRange:
            min: 1000
            max: 8000
        page: 1
        pageSize: 20

    GlobalSearchResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            total:
              type: integer
              description: 总搜索结果数
              example: 156
            results:
              type: object
              properties:
                second_hand:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 商品ID
                        example: "product_123"
                      title:
                        type: string
                        description: 商品标题
                        example: "iPhone 14 Pro Max"
                      price:
                        type: number
                        description: 价格
                        example: 7999
                      condition:
                        type: string
                        description: 成色
                        example: "95新"
                      image:
                        type: string
                        description: 商品图片
                        example: "https://images.example.com/phone1.jpg"
                      module:
                        type: string
                        description: 所属模块
                        example: "second_hand"
                housing:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 房源ID
                        example: "house_456"
                      title:
                        type: string
                        description: 房源标题
                        example: "阳光花园 2室1厅"
                      price:
                        type: number
                        description: 月租金
                        example: 3200
                      roomType:
                        type: string
                        description: 户型
                        example: "2室1厅1卫"
                      image:
                        type: string
                        description: 房源图片
                        example: "https://images.example.com/house1.jpg"
                      module:
                        type: string
                        description: 所属模块
                        example: "housing"
            suggestions:
              type: array
              items:
                type: string
              description: 搜索建议
              example: ["iPhone 13", "iPhone 14", "iPhone 15"]

    SearchSuggestionsResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            hotKeywords:
              type: array
              items:
                type: object
                properties:
                  keyword:
                    type: string
                    description: 热门关键词
                    example: "iPhone"
                  count:
                    type: integer
                    description: 搜索次数
                    example: 1250
              description: 热门搜索词
            suggestions:
              type: array
              items:
                type: object
                properties:
                  keyword:
                    type: string
                    description: 建议关键词
                    example: "iPhone 14"
                  type:
                    type: string
                    description: 建议类型
                    example: "product"
                    enum: [product, category, brand, location]
              description: 搜索建议

    DiscoverFeedsRequest:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
        feedTypes:
          type: array
          items:
            type: string
            enum: [all, community_news, activities, recommendations, social]
          description: 动态类型筛选
          example: ["community_news", "activities"]
          default: ["all"]
      example:
        page: 1
        pageSize: 20
        feedTypes: ["community_news", "activities"]

    DiscoverFeedsResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            total:
              type: integer
              description: 总记录数
              example: 89
            page:
              type: integer
              description: 当前页码
              example: 1
            pageSize:
              type: integer
              description: 每页数量
              example: 20
            feeds:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: 动态ID
                    example: "feed_123456"
                  type:
                    type: string
                    description: 动态类型
                    example: "community_news"
                    enum: [community_news, activity, recommendation, social]
                  title:
                    type: string
                    description: 动态标题
                    example: "小区物业通知：下周停水维修"
                  content:
                    type: string
                    description: 动态内容
                    example: "各位业主，下周二（1月16日）上午9:00-17:00将进行供水管道维修..."
                  images:
                    type: array
                    items:
                      type: string
                    description: 动态图片
                    example: ["https://images.example.com/notice1.jpg"]
                  author:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 发布者ID
                        example: "user_789"
                      name:
                        type: string
                        description: 发布者姓名
                        example: "物业管理处"
                      avatar:
                        type: string
                        description: 发布者头像
                        example: "https://images.example.com/property.jpg"
                      role:
                        type: string
                        description: 发布者角色
                        example: "property_manager"
                        enum: [resident, property_manager, community_admin, system]
                  publishTime:
                    type: string
                    format: date-time
                    description: 发布时间
                    example: "2024-01-15T10:30:00Z"
                  stats:
                    type: object
                    properties:
                      views:
                        type: integer
                        description: 浏览次数
                        example: 256
                      likes:
                        type: integer
                        description: 点赞数
                        example: 12
                      comments:
                        type: integer
                        description: 评论数
                        example: 5
                  isLiked:
                    type: boolean
                    description: 当前用户是否已点赞
                    example: false

    ActivitiesResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            total:
              type: integer
              description: 总记录数
              example: 45
            page:
              type: integer
              description: 当前页码
              example: 1
            pageSize:
              type: integer
              description: 每页数量
              example: 20
            activities:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: 活动ID
                    example: "activity_123456"
                  type:
                    type: string
                    description: 活动类型
                    example: "group_buy"
                    enum: [community_event, group_buy, market, social]
                  title:
                    type: string
                    description: 活动标题
                    example: "盒马鲜生水果拼单"
                  description:
                    type: string
                    description: 活动描述
                    example: "本周末去盒马采购水果，有需要带的吗？"
                  image:
                    type: string
                    description: 活动图片
                    example: "https://images.example.com/activity1.jpg"
                  organizer:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 发起人ID
                        example: "user_789"
                      name:
                        type: string
                        description: 发起人姓名
                        example: "王小花"
                      avatar:
                        type: string
                        description: 发起人头像
                        example: "https://images.example.com/avatar1.jpg"
                  startTime:
                    type: string
                    format: date-time
                    description: 活动开始时间
                    example: "2024-01-17T10:00:00Z"
                  endTime:
                    type: string
                    format: date-time
                    description: 活动结束时间
                    example: "2024-01-17T18:00:00Z"
                  participants:
                    type: object
                    properties:
                      current:
                        type: integer
                        description: 当前参与人数
                        example: 3
                      target:
                        type: integer
                        description: 目标参与人数
                        example: 5
                  status:
                    type: string
                    description: 活动状态
                    example: "ongoing"
                    enum: [upcoming, ongoing, completed, cancelled]
                  isJoined:
                    type: boolean
                    description: 当前用户是否已参与
                    example: false

    ConversationListRequest:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
        filters:
          type: object
          properties:
            type:
              type: string
              description: 会话类型筛选
              example: "private"
              enum: [all, private, group, system]
              default: all
            hasUnread:
              type: boolean
              description: 是否只显示有未读消息的会话
              example: true
            keyword:
              type: string
              description: 搜索关键词（会话标题或最后消息内容）
              example: "张三"
              maxLength: 100
        sortBy:
          type: string
          enum: [update_time_desc, update_time_asc, unread_first]
          default: update_time_desc
          description: 排序方式
      example:
        page: 1
        pageSize: 20
        filters:
          type: "private"
          hasUnread: true
          keyword: "张三"
        sortBy: "unread_first"

    MessageListRequest:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 100
          default: 50
          description: 每页数量
        filters:
          type: object
          properties:
            messageType:
              type: string
              description: 消息类型筛选
              example: "text"
              enum: [all, text, image, voice, video, file, system]
              default: all
            before:
              type: string
              description: 获取指定消息ID之前的消息（用于向上翻页）
              example: "message_123456"
            after:
              type: string
              description: 获取指定消息ID之后的消息（用于向下翻页）
              example: "message_789012"
            dateRange:
              type: object
              properties:
                startDate:
                  type: string
                  format: date-time
                  description: 开始时间
                  example: "2024-01-15T00:00:00Z"
                endDate:
                  type: string
                  format: date-time
                  description: 结束时间
                  example: "2024-01-16T23:59:59Z"
            keyword:
              type: string
              description: 搜索关键词（消息内容）
              example: "商品"
              maxLength: 100
        sortBy:
          type: string
          enum: [time_desc, time_asc]
          default: time_desc
          description: 排序方式
      example:
        page: 1
        pageSize: 50
        filters:
          messageType: "text"
          before: "message_123456"
          keyword: "商品"
        sortBy: "time_desc"

    ActivityListRequest:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
        filters:
          type: object
          properties:
            type:
              type: string
              description: 活动类型筛选
              example: "group_buy"
              enum: [all, community_event, group_buy, market, social]
              default: all
            status:
              type: string
              description: 活动状态筛选
              example: "ongoing"
              enum: [all, upcoming, ongoing, completed]
              default: all
            dateRange:
              type: object
              properties:
                startDate:
                  type: string
                  format: date
                  description: 开始日期
                  example: "2024-01-15"
                endDate:
                  type: string
                  format: date
                  description: 结束日期
                  example: "2024-01-31"
            keyword:
              type: string
              description: 搜索关键词
              example: "水果"
              maxLength: 100
        sortBy:
          type: string
          enum: [start_time_desc, start_time_asc, participants_desc, create_time_desc]
          default: start_time_desc
          description: 排序方式
      example:
        page: 1
        pageSize: 20
        filters:
          type: "group_buy"
          status: "ongoing"
          dateRange:
            startDate: "2024-01-15"
            endDate: "2024-01-31"
          keyword: "水果"
        sortBy: "start_time_desc"

    ConversationsResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            total:
              type: integer
              description: 总会话数
              example: 25
            page:
              type: integer
              description: 当前页码
              example: 1
            pageSize:
              type: integer
              description: 每页数量
              example: 20
            conversations:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: 会话ID
                    example: "conversation_123456"
                  type:
                    type: string
                    description: 会话类型
                    example: "private"
                    enum: [private, group, system]
                  title:
                    type: string
                    description: 会话标题
                    example: "张三"
                  avatar:
                    type: string
                    description: 会话头像
                    example: "https://images.example.com/avatar1.jpg"
                  lastMessage:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 最后消息ID
                        example: "message_789012"
                      content:
                        type: string
                        description: 最后消息内容
                        example: "好的，明天见"
                      type:
                        type: string
                        description: 消息类型
                        example: "text"
                        enum: [text, image, voice, video, file, system]
                      sendTime:
                        type: string
                        format: date-time
                        description: 发送时间
                        example: "2024-01-15T14:30:00Z"
                      sender:
                        type: object
                        properties:
                          id:
                            type: string
                            description: 发送者ID
                            example: "user_456"
                          name:
                            type: string
                            description: 发送者姓名
                            example: "张三"
                  unreadCount:
                    type: integer
                    description: 未读消息数
                    example: 2
                  updateTime:
                    type: string
                    format: date-time
                    description: 最后更新时间
                    example: "2024-01-15T14:30:00Z"
                  isMuted:
                    type: boolean
                    description: 是否已静音
                    example: false

    MessagesResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            total:
              type: integer
              description: 总消息数
              example: 156
            page:
              type: integer
              description: 当前页码
              example: 1
            pageSize:
              type: integer
              description: 每页数量
              example: 50
            messages:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: 消息ID
                    example: "message_123456"
                  type:
                    type: string
                    description: 消息类型
                    example: "text"
                    enum: [text, image, voice, video, file, system]
                  content:
                    type: string
                    description: 消息内容
                    example: "你好，请问这个商品还在吗？"
                  sender:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 发送者ID
                        example: "user_789"
                      name:
                        type: string
                        description: 发送者姓名
                        example: "李四"
                      avatar:
                        type: string
                        description: 发送者头像
                        example: "https://images.example.com/avatar2.jpg"
                  sendTime:
                    type: string
                    format: date-time
                    description: 发送时间
                    example: "2024-01-15T14:30:00Z"
                  status:
                    type: string
                    description: 消息状态
                    example: "delivered"
                    enum: [sending, sent, delivered, read, failed]
                  isRead:
                    type: boolean
                    description: 是否已读
                    example: true

    SendMessageRequest:
      type: object
      required:
        - type
        - content
      properties:
        type:
          type: string
          description: 消息类型
          example: "text"
          enum: [text, image, voice, video, file]
        content:
          type: string
          description: 消息内容
          example: "你好，请问这个商品还在吗？"
          maxLength: 1000
        attachments:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                description: 附件类型
                example: "image"
                enum: [image, voice, video, file]
              url:
                type: string
                description: 附件URL
                example: "https://images.example.com/attachment1.jpg"
              size:
                type: integer
                description: 附件大小（字节）
                example: 1024000
              duration:
                type: integer
                description: 音视频时长（秒）
                example: 30
          description: 消息附件
        replyTo:
          type: string
          description: 回复的消息ID
          example: "message_456789"
      example:
        type: "text"
        content: "你好，请问这个商品还在吗？"

    SendMessageResponse:
      type: object
      properties:
        code:
          type: integer
          example: 201
          description: 响应状态码
        message:
          type: string
          example: "消息发送成功"
          description: 响应消息
        data:
          type: object
          properties:
            messageId:
              type: string
              description: 消息ID
              example: "message_789012"
            sendTime:
              type: string
              format: date-time
              description: 发送时间
              example: "2024-01-15T14:30:00Z"
            status:
              type: string
              description: 消息状态
              example: "sent"
              enum: [sending, sent, delivered, read, failed]

    NotificationListRequest:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
        filters:
          type: object
          properties:
            type:
              type: string
              description: 通知类型筛选
              example: "system"
              enum: [all, system, activity, transaction, social]
              default: all
            status:
              type: string
              description: 通知状态筛选
              example: "unread"
              enum: [all, unread, read]
              default: all
            dateRange:
              type: object
              properties:
                startDate:
                  type: string
                  format: date
                  description: 开始日期
                  example: "2024-01-01"
                endDate:
                  type: string
                  format: date
                  description: 结束日期
                  example: "2024-01-31"
        sortBy:
          type: string
          enum: [time_desc, time_asc, type]
          default: time_desc
          description: 排序方式
      example:
        page: 1
        pageSize: 20
        filters:
          type: "system"
          status: "unread"
          dateRange:
            startDate: "2024-01-01"
            endDate: "2024-01-31"
        sortBy: "time_desc"

    NotificationsResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            total:
              type: integer
              description: 总通知数
              example: 45
            unreadCount:
              type: integer
              description: 未读通知数
              example: 8
            page:
              type: integer
              description: 当前页码
              example: 1
            pageSize:
              type: integer
              description: 每页数量
              example: 20
            notifications:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: 通知ID
                    example: "notification_123456"
                  type:
                    type: string
                    description: 通知类型
                    example: "system"
                    enum: [system, activity, transaction, social]
                  title:
                    type: string
                    description: 通知标题
                    example: "系统维护通知"
                  content:
                    type: string
                    description: 通知内容
                    example: "系统将于今晚22:00-24:00进行维护升级"
                  icon:
                    type: string
                    description: 通知图标
                    example: "fas fa-cog"
                  data:
                    type: object
                    description: 通知相关数据
                    example:
                      targetId: "maintenance_001"
                      targetType: "system_maintenance"
                  createTime:
                    type: string
                    format: date-time
                    description: 创建时间
                    example: "2024-01-15T10:30:00Z"
                  isRead:
                    type: boolean
                    description: 是否已读
                    example: false

    SuccessResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "操作成功"
          description: 响应消息
        data:
          type: object
          nullable: true
          description: 响应数据（可为空）

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
          example: 400
        message:
          type: string
          description: 错误消息
          example: "请求参数错误"
        error:
          type: string
          description: 详细错误信息
          example: "keyword字段不能为空"
        timestamp:
          type: string
          format: date-time
          description: 错误发生时间
          example: "2024-01-15T10:30:00Z"

tags:
  - name: 首页管理
    description: 首页仪表盘和推荐内容管理功能
  - name: 搜索功能
    description: 全局搜索和搜索建议功能
  - name: 发现页面
    description: 发现页面动态和活动管理功能
  - name: 消息管理
    description: 会话和消息管理功能
  - name: 通知管理
    description: 系统通知管理功能
