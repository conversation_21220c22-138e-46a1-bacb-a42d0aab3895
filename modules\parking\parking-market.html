<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 停车位租售市场</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow - 更微妙的阴影 */
            box-shadow: 0 2px 6px rgba(0,0,0,0.02), 0 1px 3px rgba(0,0,0,0.03);
            transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            will-change: transform;
        }
        
        .ios-card:active {
            transform: scale(0.97);
            box-shadow: 0 1px 2px rgba(0,0,0,0.03);
            background-color: rgba(0,0,0,0.01);
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            background-color: rgba(255,255,255,0.97);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            padding-bottom: max(env(safe-area-inset-bottom), 16px);
            border-top: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* iOS搜索框样式 */
        .ios-search {
            border-radius: 10px;
            background-color: var(--ios-light-gray);
            height: 36px;
            transition: background-color 0.2s ease;
            box-shadow: inset 0 0 0 0.5px rgba(0,0,0,0.03);
        }
        
        .ios-search:focus-within {
            background-color: rgba(142, 142, 147, 0.12);
            box-shadow: inset 0 0 0 0.5px rgba(0,0,0,0.08);
        }
        
        /* iOS标题文字样式 */
        .ios-section-title {
            font-weight: 600;
            letter-spacing: -0.01em;
            color: var(--ios-label);
            font-size: 20px;
        }
        
        /* iOS标签样式 */
        .ios-tag {
            font-size: 11px;
            font-weight: 500;
            letter-spacing: -0.01em;
            border-radius: 12px;
            padding: 2px 8px;
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
        }
        
        /* iOS价格文本 */
        .ios-price {
            font-weight: 600;
            color: var(--ios-red);
            letter-spacing: -0.01em;
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 500;
            background-color: rgba(255,255,255,0.97);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }

        /* 搜索栏容器 */
        .ios-search-container {
            position: sticky;
            top: 44px;
            z-index: 20;
            background-color: var(--ios-secondarySystemBackground);
            padding: 8px 16px;
            border-bottom: 0.5px solid rgba(0,0,0,0.05);
            backdrop-filter: blur(12px) saturate(180%);
            -webkit-backdrop-filter: blur(12px) saturate(180%);
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(12px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.5s cubic-bezier(0.24, 0.22, 0.015, 1.0) forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }
        
        /* iOS筛选栏样式 */
        .ios-filter-bar {
            position: sticky;
            top: 88px;
            z-index: 15;
            background-color: var(--ios-systemBackground);
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
            backdrop-filter: blur(12px) saturate(180%);
            -webkit-backdrop-filter: blur(12px) saturate(180%);
        }
        
        .ios-filter-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px 12px;
            font-size: 13px;
            color: var(--ios-secondaryLabel);
            font-weight: 500;
            transition: color 0.2s ease;
        }
        
        .ios-filter-button.active {
            color: var(--ios-blue);
        }
        
        .ios-filter-button:active {
            color: var(--ios-blue);
            opacity: 0.8;
        }
        
        /* 内容区域 */
        .ios-content-area {
            padding-top: 6px;
            padding-bottom: 110px;
        }

        /* iOS高亮文本 */
        .ios-highlight-text {
            color: var(--ios-blue);
            font-weight: 500;
        }

        /* iOS分隔线 */
        .ios-separator {
            height: 0.5px;
            background-color: rgba(60,60,67,0.1);
        }

        /* iOS底部标签栏图标 */
        .ios-tab-icon {
            font-size: 20px;
            margin-bottom: 2px;
        }

        /* iOS底部安全区域填充 */
        .ios-safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }

        /* iOS半透明背景 */
        .ios-translucent-bg {
            background-color: rgba(255,255,255,0.97);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
        }

        /* iOS弹性动画 */
        .ios-spring-animation {
            transition: all 0.5s cubic-bezier(0.24, 0.22, 0.015, 1.0);
            will-change: transform;
        }

        /* iOS轻微缩放动画 */
        @keyframes ios-subtle-scale {
            0% { transform: scale(1); }
            50% { transform: scale(0.98); }
            100% { transform: scale(1); }
        }

        .ios-subtle-scale {
            animation: ios-subtle-scale 0.3s cubic-bezier(0.24, 0.22, 0.015, 1.0);
        }

        /* iOS图片容器 */
        .ios-image-container {
            border-radius: 8px;
            overflow: hidden;
            background-color: #f2f2f7;
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator">
            <div class="screen-title">停车位租售市场</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="window.location.href='../navigation/home.html'" class="ios-button ios-haptic">
                    <i class="fas fa-chevron-left text-[#007AFF] mr-1"></i>
                    <span class="text-[#007AFF]">返回</span>
                </button>
                <h1 class="text-center font-semibold">停车位市场</h1>
                <button onclick="window.location.href='publish-parking.html'" class="ios-button ios-haptic text-[#007AFF]">
                    <i class="fas fa-plus text-[#007AFF]"></i>
                </button>
            </div>

            <!-- 搜索栏 -->
            <div class="ios-search-container">
                <div class="flex items-center ios-search px-3 ios-subtle-scale">
                    <i class="fas fa-search text-[#8E8E93] text-xs"></i>
                    <input type="text" placeholder="搜索小区名称、位置或其他关键词" class="ml-2 bg-transparent flex-1 outline-none text-sm h-full">
                </div>
            </div>
            
            <!-- 筛选条件 -->
            <div class="ios-filter-bar">
                <div class="flex justify-around py-2">
                    <button class="ios-filter-button ios-button ios-haptic active">
                        全部
                        <i class="fas fa-check ml-1 text-xs"></i>
                    </button>
                    <button class="ios-filter-button ios-button ios-haptic">
                        区域
                        <i class="fas fa-chevron-down ml-1 text-xs"></i>
                    </button>
                    <button class="ios-filter-button ios-button ios-haptic">
                        价格
                        <i class="fas fa-chevron-down ml-1 text-xs"></i>
                    </button>
                    <button class="ios-filter-button ios-button ios-haptic">
                        筛选
                        <i class="fas fa-sliders-h ml-1 text-xs"></i>
                    </button>
                </div>
            </div>

            <div class="ios-content-area">
                <!-- 快速筛选标签 -->
                <div class="px-4 pb-3 pt-1 flex items-center space-x-2 overflow-x-auto ios-scroll-indicator">
                    <span class="ios-tag bg-[#007AFF] text-white whitespace-nowrap ios-button ios-haptic">全部车位</span>
                    <span class="ios-tag whitespace-nowrap ios-button ios-haptic">出租</span>
                    <span class="ios-tag whitespace-nowrap ios-button ios-haptic">出售</span>
                    <span class="ios-tag whitespace-nowrap ios-button ios-haptic">地下车位</span>
                    <span class="ios-tag whitespace-nowrap ios-button ios-haptic">地面车位</span>
                    <span class="ios-tag whitespace-nowrap ios-button ios-haptic">有充电桩</span>
                    <span class="ios-tag whitespace-nowrap ios-button ios-haptic">临近电梯</span>
                </div>
                
                <!-- 停车位列表 -->
                <div class="px-4 space-y-3">
                    <div class="ios-card overflow-hidden ios-haptic ios-fade-in" onclick="window.location.href='parking-detail.html'">
                        <div class="flex p-4">
                            <div class="ios-image-container w-20 h-20 flex-shrink-0 ios-spring-animation">
                                <img src="https://images.unsplash.com/photo-1573348722427-f1d6819fdf98" class="w-full h-full object-cover">
                            </div>
                            <div class="flex-1 ml-3">
                                <div class="flex justify-between">
                                    <h3 class="text-[15px] font-semibold">春题杭玥府停车位</h3>
                                    <p class="ios-price text-[15px]">¥280,000</p>
                                </div>
                                <p class="text-xs text-[#8E8E93] mt-1">地下负一层 | 固定车位</p>
                                <div class="flex flex-wrap items-center gap-2 mt-2">
                                    <span class="ios-tag" style="background-color: rgba(255,45,85,0.1); color: var(--ios-pink);">产权车位</span>
                                    <span class="ios-tag">带储物间</span>
                                    <span class="ios-tag" style="background-color: rgba(52,199,89,0.1); color: var(--ios-green);">可分期</span>
                                </div>
                                <p class="text-xs text-[#8E8E93] mt-2 flex items-center">
                                    <i class="fas fa-map-marker-alt text-[#8E8E93] mr-1 text-[10px]"></i>
                                    距离地铁4号线吴家脚港站步行7分钟
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="ios-card overflow-hidden ios-haptic ios-fade-in" style="animation-delay: 0.1s;" onclick="window.location.href='parking-detail.html'">
                        <div class="flex p-4">
                            <div class="ios-image-container w-20 h-20 flex-shrink-0 ios-spring-animation">
                                <img src="https://images.unsplash.com/photo-1573348722427-f1d6819fdf98" class="w-full h-full object-cover">
                            </div>
                            <div class="flex-1 ml-3">
                                <div class="flex justify-between">
                                    <h3 class="text-[15px] font-semibold">阳光花园地下停车位</h3>
                                    <p class="ios-price text-[15px]">¥300/月</p>
                                </div>
                                <p class="text-xs text-[#8E8E93] mt-1">地下负一层 | 固定车位</p>
                                <div class="flex flex-wrap items-center gap-2 mt-2">
                                    <span class="ios-tag">24小时保安</span>
                                    <span class="ios-tag" style="background-color: rgba(52,199,89,0.1); color: var(--ios-green);">可短租</span>
                                </div>
                                <p class="text-xs text-[#8E8E93] mt-2 flex items-center">
                                    <i class="fas fa-map-marker-alt text-[#8E8E93] mr-1 text-[10px]"></i>
                                    距离地铁2号线阳光花园站步行5分钟
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="ios-card overflow-hidden ios-haptic ios-fade-in" style="animation-delay: 0.15s;" onclick="window.location.href='parking-detail.html'">
                        <div class="flex p-4">
                            <div class="ios-image-container w-20 h-20 flex-shrink-0 ios-spring-animation">
                                <img src="https://images.unsplash.com/photo-1573348722427-f1d6819fdf98" class="w-full h-full object-cover">
                            </div>
                            <div class="flex-1 ml-3">
                                <div class="flex justify-between">
                                    <h3 class="text-[15px] font-semibold">幸福小区地下车位</h3>
                                    <p class="ios-price text-[15px]">¥350/月</p>
                                </div>
                                <p class="text-xs text-[#8E8E93] mt-1">地下负二层 | 固定车位</p>
                                <div class="flex flex-wrap items-center gap-2 mt-2">
                                    <span class="ios-tag" style="background-color: rgba(0,122,255,0.1); color: var(--ios-blue);">有充电桩</span>
                                    <span class="ios-tag" style="background-color: rgba(255,149,0,0.1); color: var(--ios-orange);">临近地铁</span>
                                </div>
                                <p class="text-xs text-[#8E8E93] mt-2 flex items-center">
                                    <i class="fas fa-map-marker-alt text-[#8E8E93] mr-1 text-[10px]"></i>
                                    距离地铁1号线幸福站步行3分钟
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="ios-card overflow-hidden ios-haptic ios-fade-in" style="animation-delay: 0.2s;" onclick="window.location.href='parking-detail.html'">
                        <div class="flex p-4">
                            <div class="ios-image-container w-20 h-20 flex-shrink-0 ios-spring-animation">
                                <img src="https://images.unsplash.com/photo-1573348722427-f1d6819fdf98" class="w-full h-full object-cover">
                            </div>
                            <div class="flex-1 ml-3">
                                <div class="flex justify-between">
                                    <h3 class="text-[15px] font-semibold">和谐家园地面车位</h3>
                                    <p class="ios-price text-[15px]">¥280/月</p>
                                </div>
                                <p class="text-xs text-[#8E8E93] mt-1">地面停车场 | 固定车位</p>
                                <div class="flex flex-wrap items-center gap-2 mt-2">
                                    <span class="ios-tag" style="background-color: rgba(88,86,214,0.1); color: var(--ios-purple);">监控覆盖</span>
                                    <span class="ios-tag" style="background-color: rgba(52,199,89,0.1); color: var(--ios-green);">可月付</span>
                                </div>
                                <p class="text-xs text-[#8E8E93] mt-2 flex items-center">
                                    <i class="fas fa-map-marker-alt text-[#8E8E93] mr-1 text-[10px]"></i>
                                    小区内地面停车位，环境安全
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="fixed bottom-0 left-0 right-0 ios-bottom-nav flex justify-around pt-2 pb-6 z-40">
                <button onclick="window.location.href='../navigation/home.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-home ios-tab-icon text-[#8E8E93] mb-0.5"></i>
                    <span class="text-[10px] text-[#8E8E93]">首页</span>
                </button>
                <button onclick="window.location.href='../navigation/discover.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-compass ios-tab-icon text-[#8E8E93] mb-0.5"></i>
                    <span class="text-[10px] text-[#8E8E93]">发现</span>
                </button>
                <button onclick="window.location.href='publish-parking.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <div class="w-[50px] h-[50px] bg-[#007AFF] rounded-full flex items-center justify-center -mt-5 shadow-lg ios-spring-animation" style="box-shadow: 0 3px 10px rgba(0,122,255,0.3);">
                        <i class="fas fa-plus text-white text-xl"></i>
                    </div>
                </button>
                <button onclick="window.location.href='../navigation/messages.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-comment ios-tab-icon text-[#8E8E93] mb-0.5"></i>
                    <span class="text-[10px] text-[#8E8E93]">消息</span>
                </button>
                <button class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-user ios-tab-icon text-[#8E8E93] mb-0.5"></i>
                    <span class="text-[10px] text-[#8E8E93]">我的</span>
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载后执行的脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
        });
    </script>
</body>
</html> 