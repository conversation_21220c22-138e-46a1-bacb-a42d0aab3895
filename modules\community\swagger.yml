openapi: 3.0.3
info:
  title: 乐享友邻 - 社区管理API
  description: 社区管理平台后端接口文档，包含小区选择、绑定、注册申请等功能
  version: 1.0.0

servers:
  - url: https://dev-api.hoodly-joy.com/v1
    description: 开发环境

security:
  - BearerAuth: []

paths:
  /communities:
    post:
      tags:
        - 社区管理
      summary: 获取小区列表
      description: 根据用户位置获取推荐小区列表，支持搜索功能
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommunityListRequest'
      responses:
        '200':
          description: 成功获取小区列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommunityListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /communities/search:
    post:
      tags:
        - 社区管理
      summary: 搜索小区
      description: 根据关键词搜索小区
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommunitySearchRequest'
      responses:
        '200':
          description: 搜索成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommunityListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /communities/{communityId}/bind:
    post:
      tags:
        - 社区管理
      summary: 绑定小区
      description: 用户绑定到指定小区
      parameters:
        - name: communityId
          in: path
          required: true
          description: 小区唯一标识ID
          schema:
            type: string
            example: "community_123456"
      responses:
        '200':
          description: 绑定成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BindCommunityResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /communities/register:
    post:
      tags:
        - 社区管理
      summary: 申请注册小区
      description: 用户申请注册新的小区
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterCommunityRequest'
      responses:
        '201':
          description: 申请提交成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterCommunityResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user/current-community:
    get:
      tags:
        - 用户管理
      summary: 获取用户当前绑定的小区
      description: 获取当前用户绑定的小区信息
      responses:
        '200':
          description: 成功获取当前小区信息
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CurrentCommunityResponse'
        '404':
          description: 用户未绑定小区
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /upload/community-documents:
    post:
      tags:
        - 文件上传
      summary: 上传小区注册证明材料
      description: 上传小区注册申请相关的证明材料图片
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: 证明材料图片文件数组
                documentType:
                  type: string
                  enum: [property_certificate, property_management_proof, residence_proof, other]
                  description: 证明材料类型
      responses:
        '200':
          description: 上传成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadResponse'
        '400':
          description: 上传失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    CommunityListRequest:
      type: object
      properties:
        location:
          type: object
          properties:
            latitude:
              type: number
              description: 用户当前位置纬度
              example: 30.2741
            longitude:
              type: number
              description: 用户当前位置经度
              example: 120.1551
        radius:
          type: number
          description: 搜索半径（公里）
          example: 10
          default: 10
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
      example:
        location:
          latitude: 30.2741
          longitude: 120.1551
        radius: 10
        page: 1
        pageSize: 20

    CommunityListResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            currentCommunity:
              $ref: '#/components/schemas/CommunityInfo'
              nullable: true
              description: 用户当前绑定的小区（如有）
            recommendedCommunities:
              type: array
              items:
                $ref: '#/components/schemas/CommunityInfo'
              description: 推荐小区列表
            total:
              type: integer
              description: 总记录数
              example: 25
            page:
              type: integer
              description: 当前页码
              example: 1
            pageSize:
              type: integer
              description: 每页数量
              example: 20

    CommunityInfo:
      type: object
      properties:
        id:
          type: string
          description: 小区ID
          example: "community_123456"
        name:
          type: string
          description: 小区名称
          example: "春题·杭玥府"
        address:
          type: string
          description: 小区地址
          example: "杭州市拱墅区桃园社区"
        fullAddress:
          type: string
          description: 完整地址
          example: "杭州市拱墅区桃园社区春题·杭玥府"
        location:
          type: object
          properties:
            latitude:
              type: number
              description: 纬度
              example: 30.2741
            longitude:
              type: number
              description: 经度
              example: 120.1551
        distance:
          type: number
          description: 距离用户的距离（公里）
          example: 0.8
        residentCount:
          type: integer
          description: 居民数量
          example: 1250
        buildingCount:
          type: integer
          description: 楼栋数量
          example: 12
        status:
          type: string
          enum: [active, pending, inactive]
          description: 小区状态
          example: "active"
        isBound:
          type: boolean
          description: 是否为用户当前绑定的小区
          example: false

    CommunitySearchRequest:
      type: object
      properties:
        keyword:
          type: string
          description: 搜索关键词（小区名称或地址）
          example: "春题"
          maxLength: 100
        location:
          type: object
          properties:
            latitude:
              type: number
              description: 用户当前位置纬度
              example: 30.2741
            longitude:
              type: number
              description: 用户当前位置经度
              example: 120.1551
        radius:
          type: number
          description: 搜索半径（公里）
          example: 20
          default: 20
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
      example:
        keyword: "春题"
        location:
          latitude: 30.2741
          longitude: 120.1551
        radius: 20
        page: 1
        pageSize: 20

    BindCommunityResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "绑定成功"
          description: 响应消息
        data:
          type: object
          properties:
            communityId:
              type: string
              description: 绑定的小区ID
              example: "community_123456"
            communityName:
              type: string
              description: 绑定的小区名称
              example: "春题·杭玥府"
            bindTime:
              type: string
              format: date-time
              description: 绑定时间
              example: "2024-01-15T10:30:00Z"

    RegisterCommunityRequest:
      type: object
      required:
        - communityName
        - province
        - city
        - district
        - address
        - applicantName
        - applicantPhone
        - applicantIdentity
      properties:
        communityName:
          type: string
          description: 小区名称
          example: "阳光花园"
          maxLength: 100
        province:
          type: string
          description: 省份
          example: "浙江省"
        city:
          type: string
          description: 城市
          example: "杭州市"
        district:
          type: string
          description: 区域
          example: "拱墅区"
        address:
          type: string
          description: 详细地址
          example: "拱墅区湖州街200号"
          maxLength: 200
        applicantName:
          type: string
          description: 申请人姓名
          example: "张三"
          maxLength: 50
        applicantPhone:
          type: string
          description: 申请人联系电话
          example: "13800138000"
          pattern: '^1[3-9]\d{9}$'
        applicantIdentity:
          type: string
          description: 申请人身份
          example: "业主"
          enum: ["业主", "物业", "租户"]
        buildingInfo:
          type: object
          properties:
            building:
              type: string
              description: 楼栋信息
              example: "3栋2单元"
            roomNumber:
              type: string
              description: 房间号
              example: "1201"
        additionalNotes:
          type: string
          description: 补充说明
          example: "小区环境优美，管理规范"
          maxLength: 500
        documents:
          type: array
          items:
            type: object
            properties:
              url:
                type: string
                description: 证明材料图片URL
                example: "https://images.example.com/document1.jpg"
              type:
                type: string
                enum: [property_certificate, property_management_proof, residence_proof, other]
                description: 证明材料类型
                example: "property_certificate"
              description:
                type: string
                description: 材料描述
                example: "房产证"
          description: 证明材料列表
      example:
        communityName: "阳光花园"
        province: "浙江省"
        city: "杭州市"
        district: "拱墅区"
        address: "拱墅区湖州街200号"
        applicantName: "张三"
        applicantPhone: "13800138000"
        applicantIdentity: "业主"
        buildingInfo:
          building: "3栋2单元"
          roomNumber: "1201"
        additionalNotes: "小区环境优美，管理规范"
        documents:
          - url: "https://images.example.com/document1.jpg"
            type: "property_certificate"
            description: "房产证"

    RegisterCommunityResponse:
      type: object
      properties:
        code:
          type: integer
          example: 201
          description: 响应状态码
        message:
          type: string
          example: "申请提交成功"
          description: 响应消息
        data:
          type: object
          properties:
            applicationId:
              type: string
              description: 申请ID
              example: "application_789012"
            status:
              type: string
              description: 申请状态
              example: "pending_review"
              enum: ["pending_review", "approved", "rejected", "under_review"]
            submitTime:
              type: string
              format: date-time
              description: 提交时间
              example: "2024-01-15T10:30:00Z"
            estimatedReviewTime:
              type: string
              description: 预计审核时间
              example: "3-5个工作日"

    CurrentCommunityResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            community:
              $ref: '#/components/schemas/CommunityInfo'
            bindTime:
              type: string
              format: date-time
              description: 绑定时间
              example: "2024-01-10T08:30:00Z"
            userRole:
              type: string
              enum: [resident, owner, tenant, property_manager]
              description: 用户在小区中的角色
              example: "owner"

    UploadResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "上传成功"
          description: 响应消息
        data:
          type: object
          properties:
            documents:
              type: array
              items:
                type: object
                properties:
                  url:
                    type: string
                    description: 文件URL
                    example: "https://images.example.com/document1.jpg"
                  filename:
                    type: string
                    description: 文件名
                    example: "property_certificate.jpg"
                  size:
                    type: integer
                    description: 文件大小（字节）
                    example: 2048000
                  type:
                    type: string
                    description: 文件类型
                    example: "property_certificate"

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
          example: 400
        message:
          type: string
          description: 错误消息
          example: "请求参数错误"
        error:
          type: string
          description: 详细错误信息
          example: "communityName字段不能为空"
        timestamp:
          type: string
          format: date-time
          description: 错误发生时间
          example: "2024-01-15T10:30:00Z"

tags:
  - name: 社区管理
    description: 小区相关的管理功能，包括列表查询、搜索、绑定等
  - name: 用户管理
    description: 用户相关的社区信息管理
  - name: 文件上传
    description: 证明材料等文件上传功能
