<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no">
    <title>乐享友邻 - 邻里集市</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-separator: rgba(60,60,67,0.1);
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: rgba(60,60,67,0.6); /* 60% opacity */
            --ios-tertiaryLabel: rgba(60,60,67,0.3); /* 30% opacity */
            --ios-quaternaryLabel: rgba(60,60,67,0.18); /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
            
            /* iOS圆角半径 */
            --ios-corner-radius-small: 6px;
            --ios-corner-radius-medium: 10px;
            --ios-corner-radius-large: 14px;
            --ios-corner-radius-xl: 18px;
        }

        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
            touch-action: manipulation;
            user-select: none;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            background-color: var(--ios-card);
            border-radius: var(--ios-corner-radius-large);
            overflow: hidden;
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        box-shadow 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .ios-card:active {
            transform: scale(0.98);
            box-shadow: 0 1px 3px rgba(0,0,0,0.02);
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 状态栏 */
        .ios-device-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 15px;
            font-weight: 600;
            background-color: var(--ios-systemBackground);
            position: sticky;
            top: 0;
            z-index: 40;
        }
        
        /* 导航栏 */
        .ios-nav-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            position: sticky;
            top: 44px;
            z-index: 35;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-nav-back {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
            display: flex;
            align-items: center;
        }
        
        .ios-nav-title {
            font-size: 17px;
            font-weight: 600;
            color: var(--ios-label);
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            padding-bottom: env(safe-area-inset-bottom);
            border-top: 0.5px solid var(--ios-separator);
        }
        
        /* iOS搜索框 */
        .ios-search {
            display: flex;
            align-items: center;
            background-color: var(--ios-secondarySystemBackground);
            border-radius: var(--ios-corner-radius-large);
            padding: 8px 12px;
            transition: background-color 0.2s ease;
        }
        
        .ios-search:focus-within {
            background-color: #E0E0E6;
        }
        
        .ios-search input {
            background-color: transparent;
            border: none;
            outline: none;
            font-size: 17px;
            flex: 1;
            color: var(--ios-label);
        }
        
        .ios-search input::placeholder {
            color: var(--ios-tertiaryLabel);
        }
        
        /* iOS类别按钮 */
        .ios-category-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 0;
            transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .ios-category-btn:active {
            transform: scale(0.95);
        }
        
        .ios-category-icon {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 25px;
            margin-bottom: 8px;
            font-size: 20px;
        }
        
        /* iOS分隔线 */
        .ios-separator {
            height: 0.5px;
            background-color: var(--ios-separator);
            width: 100%;
        }
        
        /* iOS标签样式 */
        .ios-tag {
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
            padding: 2px 6px;
        }
        
        /* iOS触感反馈 */
        .ios-haptic {
            cursor: pointer;
        }

        /* iOS分数和评价样式 */
        .ios-rating {
            display: flex;
            align-items: center;
        }
        
        .ios-rating-stars {
            display: flex;
            margin-right: 4px;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(8px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in-delay-1 { animation-delay: 0.05s; }
        .ios-fade-in-delay-2 { animation-delay: 0.1s; }
        .ios-fade-in-delay-3 { animation-delay: 0.15s; }
        .ios-fade-in-delay-4 { animation-delay: 0.2s; }
        
        /* 图片加载优化 */
        .img-container {
            background-color: var(--ios-secondarySystemBackground);
            background-size: cover;
            background-position: center;
            transition: opacity 0.3s ease-in-out;
        }
        
        .img-lazy {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }
        
        .img-lazy.loaded {
            opacity: 1;
        }
        
        .banner-img {
            background-image: url('./images/market-banner.svg');
            background-size: cover;
            background-position: center;
            background-color: #fcf5e9; /* 温暖的背景色 */
        }
        
        .chicken-img {
            background-image: url('./images/chicken-icon.svg');
            background-color: #fff8e5;
        }
        
        .fish-img {
            background-image: url('./images/fish-icon.svg');
            background-color: #e5f6ff;
        }
        
        .farmer1-img {
            background-image: url('./images/farmer-icon.svg');
            background-color: #f5f5f5;
        }
        
        .farmer2-img {
            background-image: url('./images/farmer-icon.svg');
            background-color: #f5f5f5;
        }
        
        .category-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .category-scroll {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        /* iOS小区切换按钮 */
        .ios-location-button {
            display: flex;
            align-items: center;
            padding: 6px 12px;
            background-color: rgba(0,122,255,0.1);
            border-radius: 16px;
            color: var(--ios-blue);
            font-weight: 500;
            font-size: 13px;
        }

        /* iOS滑动和弹性效果 */
        .ios-scroll-container {
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
            overscroll-behavior-y: contain;
            height: 100%;
            padding-bottom: env(safe-area-inset-bottom, 20px);
        }

        .ios-scroll-container::-webkit-scrollbar {
            display: none;
        }

        /* iOS触感反馈增强 */
        @keyframes ios-button-press {
            0% { transform: scale(1); }
            50% { transform: scale(0.96); }
            100% { transform: scale(1); }
        }

        @keyframes ios-button-light-press {
            0% { transform: scale(1); }
            50% { transform: scale(0.98); }
            100% { transform: scale(1); }
        }

        .ios-press-animation {
            animation: ios-button-press 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .ios-light-press-animation {
            animation: ios-button-light-press 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* iOS过渡动画增强 */
        .ios-transition {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .ios-spring-transition {
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        /* iOS手势交互 */
        .ios-swipeable {
            touch-action: pan-y;
            user-select: none;
        }

        /* iOS状态变化效果 */
        .ios-active-scale {
            transform: scale(0.96);
        }

        .ios-active-opacity {
            opacity: 0.7;
        }

        .ios-highlight {
            position: relative;
            overflow: hidden;
        }

        .ios-highlight::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.1);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .ios-highlight:active::after {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen">
            <div class="screen-title">邻里集市</div>
            
            <!-- 设备状态栏 -->
            <div class="ios-device-status-bar">
                <div>9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="ios-nav-bar">
                <button onclick="history.back()" class="ios-button ios-haptic ios-nav-back">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <div class="ios-nav-title">邻里集市</div>
                <a href="saved-items.html" class="ios-button ios-haptic text-[#007AFF]">
                    <i class="far fa-heart"></i>
                </a>
            </div>

            <!-- 搜索栏 -->
            <div class="p-4 bg-white border-b border-[var(--ios-separator)]">
                <div class="ios-search">
                    <i class="fas fa-search text-[var(--ios-tertiaryLabel)] mr-2"></i>
                    <input type="text" placeholder="搜索新鲜农产品" class="ios-haptic">
                </div>
            </div>
            
            <!-- 当前小区信息栏 -->
            <div class="px-4 py-3 flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-[rgba(0,122,255,0.1)] flex items-center justify-center mr-2">
                        <i class="fas fa-map-marker-alt text-[#007AFF] text-xs"></i>
                    </div>
                    <div>
                        <div class="text-[15px] font-medium">春题·杭玥府</div>
                        <div class="text-xs text-[#8E8E93]">5栋1单元1302</div>
                    </div>
                </div>
                <button onclick="window.location.href='my-communities.html'" class="ios-location-button ios-button">
                    <span>切换小区</span>
                    <i class="fas fa-chevron-down ml-1 text-[10px]"></i>
                </button>
            </div>

            <!-- 分类导航 -->
            <div class="bg-white grid grid-cols-4 gap-3 p-4 ios-fade-in ios-fade-in-delay-1">
                <button class="ios-category-btn ios-haptic" onclick="window.location.href='category-poultry.html'">
                    <div class="ios-category-icon bg-red-100">
                        <i class="fas fa-drumstick-bite text-red-500"></i>
                    </div>
                    <span class="text-xs">家禽家畜</span>
                </button>
                <button class="ios-category-btn ios-haptic" onclick="window.location.href='category-fish.html'">
                    <div class="ios-category-icon bg-blue-100">
                        <i class="fas fa-fish text-blue-500"></i>
                    </div>
                    <span class="text-xs">鱼虾水产</span>
                </button>
                <button class="ios-category-btn ios-haptic" onclick="window.location.href='category-vegetables.html'">
                    <div class="ios-category-icon bg-green-100">
                        <i class="fas fa-carrot text-green-500"></i>
                    </div>
                    <span class="text-xs">蔬菜瓜果</span>
                </button>
                <button class="ios-category-btn ios-haptic" onclick="window.location.href='category-eggs.html'">
                    <div class="ios-category-icon bg-yellow-100">
                        <i class="fas fa-egg text-yellow-500"></i>
                    </div>
                    <span class="text-xs">禽蛋</span>
                </button>
            </div>

            <!-- 轮播推荐 -->
            <div class="m-4 ios-card ios-fade-in ios-fade-in-delay-2">
                <div class="p-3">
                    <h2 class="text-base font-semibold mb-3">今日精选</h2>
                    <div class="relative h-40 banner-img rounded-lg overflow-hidden" style="box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);">
                        <div class="absolute inset-0 bg-gradient-to-r from-amber-500/20 to-transparent"></div>
                        <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/60 to-transparent text-white">
                            <h3 class="font-bold">邻里鲜活 · 春季尝鲜</h3>
                            <p class="text-sm">直接对接周边农户，品质保证</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 热卖商品 -->
            <div class="mx-4 mt-2 ios-card ios-fade-in ios-fade-in-delay-3">
                <div class="p-3">
                    <div class="flex justify-between items-center mb-3">
                        <h2 class="text-base font-semibold">热卖产品</h2>
                        <a href="all-products.html" class="ios-button ios-haptic text-sm text-[var(--ios-blue)] flex items-center">
                            查看更多
                            <i class="fas fa-chevron-right ml-1 text-xs"></i>
                        </a>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="bg-[var(--ios-secondarySystemBackground)] rounded-lg p-3 ios-button ios-haptic" onclick="window.location.href='product-detail.html?id=1'">
                            <div class="relative">
                                <div class="w-full aspect-square img-container chicken-img rounded-lg mb-2">
                                    <img src="https://images.unsplash.com/photo-1518492104633-130d0cc84637" 
                                         class="w-full h-full object-cover rounded-lg img-lazy" 
                                         alt="散养土鸡"
                                         loading="lazy"
                                         onerror="this.style.display='none'"
                                         onload="this.classList.add('loaded')">
                                </div>
                                <span class="absolute top-2 left-2 text-xs bg-[var(--ios-red)] text-white px-2 py-1 rounded-full text-xs">热卖</span>
                            </div>
                            <h3 class="text-sm font-medium">散养土鸡</h3>
                            <p class="text-xs text-[var(--ios-secondaryLabel)] mt-1">王阿姨家散养 | 现杀现发</p>
                            <div class="flex justify-between items-center mt-2">
                                <p class="text-sm text-[var(--ios-red)] font-medium">¥38.8/只</p>
                                <span class="text-xs text-[var(--ios-secondaryLabel)]">已售52件</span>
                            </div>
                        </div>
                        <div class="bg-[var(--ios-secondarySystemBackground)] rounded-lg p-3 ios-button ios-haptic" onclick="window.location.href='product-detail.html?id=2'">
                            <div class="w-full aspect-square img-container fish-img rounded-lg mb-2">
                                <img src="./images/fish.jpg" 
                                     class="w-full h-full object-cover rounded-lg img-lazy" 
                                     alt="新鲜草鱼"
                                     loading="lazy"
                                     onerror="this.style.display='none'"
                                     onload="this.classList.add('loaded')">
                            </div>
                            <h3 class="text-sm font-medium">新鲜草鱼</h3>
                            <p class="text-xs text-[var(--ios-secondaryLabel)] mt-1">李叔家池塘 | 活杀现发</p>
                            <div class="flex justify-between items-center mt-2">
                                <p class="text-sm text-[var(--ios-red)] font-medium">¥16.8/斤</p>
                                <span class="text-xs text-[var(--ios-secondaryLabel)]">已售46件</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 附近农户 -->
            <div class="mx-4 mt-4 ios-card ios-fade-in ios-fade-in-delay-4">
                <div class="p-3">
                    <div class="flex justify-between items-center mb-3">
                        <h2 class="text-base font-semibold">附近农户</h2>
                        <a href="farmers-list.html" class="ios-button ios-haptic text-sm text-[var(--ios-blue)] flex items-center">
                            查看更多
                            <i class="fas fa-chevron-right ml-1 text-xs"></i>
                        </a>
                    </div>
                    <div class="space-y-3">
                        <div class="bg-[var(--ios-secondarySystemBackground)] rounded-lg p-3 flex space-x-3 ios-button ios-haptic" onclick="window.location.href='farmer-detail.html?id=1'">
                            <div class="w-16 h-16 img-container farmer1-img rounded-full flex-shrink-0">
                                <img src="./images/farmer.jpg" 
                                     class="w-full h-full object-cover rounded-full img-lazy" 
                                     alt="张大叔家的农场"
                                     loading="lazy"
                                     onerror="this.style.display='none'"
                                     onload="this.classList.add('loaded')">
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-sm font-medium">张大叔家的农场</h3>
                                    <span class="text-xs text-[var(--ios-secondaryLabel)]">1.2km</span>
                                </div>
                                <p class="text-xs text-[var(--ios-secondaryLabel)] mt-1">主营：散养土鸡、土鸡蛋、自种蔬菜</p>
                                <div class="ios-rating mt-2">
                                    <div class="ios-rating-stars">
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star-half-alt text-yellow-400 text-xs"></i>
                                    </div>
                                    <span class="text-xs text-[var(--ios-secondaryLabel)]">4.8 (128评)</span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-[var(--ios-secondarySystemBackground)] rounded-lg p-3 flex space-x-3 ios-button ios-haptic" onclick="window.location.href='farmer-detail.html?id=2'">
                            <div class="w-16 h-16 img-container farmer1-img rounded-full flex-shrink-0">
                                <img src="./images/farmer.jpg" 
                                     class="w-full h-full object-cover rounded-full img-lazy" 
                                     alt="李叔的鱼塘"
                                     loading="lazy"
                                     onerror="this.style.display='none'"
                                     onload="this.classList.add('loaded')">
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-sm font-medium">李叔的鱼塘</h3>
                                    <span class="text-xs text-[var(--ios-secondaryLabel)]">2.5km</span>
                                </div>
                                <p class="text-xs text-[var(--ios-secondaryLabel)] mt-1">主营：草鱼、鲤鱼、鲫鱼、小龙虾</p>
                                <div class="ios-rating mt-2">
                                    <div class="ios-rating-stars">
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-yellow-400 text-xs"></i>
                                        <i class="fas fa-star text-gray-300 text-xs"></i>
                                    </div>
                                    <span class="text-xs text-[var(--ios-secondaryLabel)]">4.2 (87评)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航 -->
            <div class="fixed bottom-0 left-0 right-0 ios-bottom-nav py-2 px-4">
                <div class="flex justify-around">
                    <button class="flex flex-col items-center ios-button ios-haptic px-4">
                        <i class="fas fa-home text-[var(--ios-blue)] text-xl"></i>
                        <span class="text-xs text-[var(--ios-blue)] mt-1">首页</span>
                    </button>
                    <button class="flex flex-col items-center ios-button ios-haptic px-4">
                        <i class="fas fa-compass text-[var(--ios-secondaryLabel)] text-xl"></i>
                        <span class="text-xs text-[var(--ios-secondaryLabel)] mt-1">发现</span>
                    </button>
                    <button class="flex flex-col items-center ios-button ios-haptic px-4">
                        <div class="w-12 h-12 bg-[var(--ios-blue)] rounded-full flex items-center justify-center -mt-4">
                            <i class="fas fa-plus text-white text-xl"></i>
                        </div>
                    </button>
                    <button class="flex flex-col items-center ios-button ios-haptic px-4">
                        <i class="fas fa-comment text-[var(--ios-secondaryLabel)] text-xl"></i>
                        <span class="text-xs text-[var(--ios-secondaryLabel)] mt-1">消息</span>
                    </button>
                    <button class="flex flex-col items-center ios-button ios-haptic px-4">
                        <i class="fas fa-user text-[var(--ios-secondaryLabel)] text-xl"></i>
                        <span class="text-xs text-[var(--ios-secondaryLabel)] mt-1">我的</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // iOS触感反馈增强
            function addHapticFeedback(intensity = 'medium') {
                if ('vibrate' in navigator) {
                    switch (intensity) {
                        case 'light':
                            navigator.vibrate(3);
                            break;
                        case 'medium':
                            navigator.vibrate(8);
                            break;
                        case 'heavy':
                            navigator.vibrate([8, 15, 8]);
                            break;
                    }
                }
            }

            // 为所有可交互元素添加触感反馈
            const interactiveElements = document.querySelectorAll('.ios-button, .ios-haptic');
            interactiveElements.forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.classList.add('ios-press-animation');
                    addHapticFeedback('light');
                });

                element.addEventListener('touchend', function() {
                    this.classList.remove('ios-press-animation');
                });
            });

            // 滚动效果优化
            const scrollContainer = document.querySelector('.ios-scroll-container');
            let lastScrollTop = 0;
            let scrollTimeout;

            scrollContainer.addEventListener('scroll', function() {
                clearTimeout(scrollTimeout);
                
                const currentScrollTop = this.scrollTop;
                const scrollDiff = currentScrollTop - lastScrollTop;
                
                // 滚动方向变化时添加触感反馈
                if ((scrollDiff > 0 && lastScrollTop <= 0) || 
                    (scrollDiff < 0 && currentScrollTop <= 0)) {
                    addHapticFeedback('light');
                }
                
                lastScrollTop = currentScrollTop;
                
                // 滚动停止检测
                scrollTimeout = setTimeout(() => {
                    if (currentScrollTop <= 0) {
                        addHapticFeedback('medium');
                    }
                }, 150);
            }, { passive: true });

            // 图片加载优化
            const lazyImages = document.querySelectorAll('.img-lazy');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.add('ios-spring-transition');
                        img.addEventListener('load', () => {
                            img.classList.add('loaded');
                        });
                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px'
            });

            lazyImages.forEach(img => imageObserver.observe(img));

            // 添加下拉刷新效果
            let touchStartY = 0;
            let pullDistance = 0;
            const PULL_THRESHOLD = 80;
            const refreshIndicator = document.createElement('div');
            refreshIndicator.className = 'refresh-indicator';
            refreshIndicator.style.cssText = `
                position: absolute;
                top: -50px;
                left: 50%;
                transform: translateX(-50%);
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 50px;
            `;

            scrollContainer.addEventListener('touchstart', (e) => {
                if (scrollContainer.scrollTop <= 0) {
                    touchStartY = e.touches[0].clientY;
                }
            }, { passive: true });

            scrollContainer.addEventListener('touchmove', (e) => {
                if (scrollContainer.scrollTop <= 0 && e.touches[0].clientY > touchStartY) {
                    pullDistance = e.touches[0].clientY - touchStartY;
                    if (pullDistance > 0 && pullDistance < PULL_THRESHOLD) {
                        scrollContainer.style.transform = `translateY(${pullDistance}px)`;
                        e.preventDefault();
                    }
                }
            }, { passive: false });

            scrollContainer.addEventListener('touchend', () => {
                if (pullDistance > 0) {
                    if (pullDistance > PULL_THRESHOLD) {
                        // 触发刷新
                        addHapticFeedback('heavy');
                        refreshContent();
                    }
                    scrollContainer.style.transform = '';
                    pullDistance = 0;
                }
            });

            function refreshContent() {
                // 模拟刷新操作
                setTimeout(() => {
                    addHapticFeedback('medium');
                    // 这里添加实际的刷新逻辑
                }, 1500);
            }

            // 卡片手势交互
            const cards = document.querySelectorAll('.ios-card');
            cards.forEach(card => {
                let startX = 0;
                let currentX = 0;

                card.addEventListener('touchstart', (e) => {
                    startX = e.touches[0].clientX;
                    card.style.transition = 'none';
                });

                card.addEventListener('touchmove', (e) => {
                    currentX = e.touches[0].clientX - startX;
                    if (Math.abs(currentX) < 100) {
                        card.style.transform = `translateX(${currentX}px)`;
                    }
                });

                card.addEventListener('touchend', () => {
                    card.style.transition = 'transform 0.3s ease';
                    card.style.transform = '';
                });
            });
        });
    </script>
</body>
</html> 