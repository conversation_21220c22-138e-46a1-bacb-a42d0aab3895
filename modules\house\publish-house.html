<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 发布房源</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
        }
        
        /* 深色模式支持 - 已注释掉，避免系统默认使用深色模式 */
        @media (prefers-color-scheme: dark) {
            body.dark-theme {
                --ios-systemBackground: #000000;
                --ios-secondarySystemBackground: #1C1C1E;
                --ios-tertiarySystemBackground: #2C2C2E;
                --ios-groupedBackground: #000000;
                --ios-card: #1C1C1E;
                
                --ios-label: #FFFFFF;
                --ios-secondaryLabel: #EBEBF599;
                --ios-tertiaryLabel: #EBEBF54D;
                --ios-quaternaryLabel: #EBEBF52E;
            }
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow */
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding-bottom: env(safe-area-inset-bottom);
            border-top: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: 0.7;
        }
        
        /* iOS标题文字样式 */
        .ios-section-title {
            font-weight: 600;
            letter-spacing: -0.01em;
            color: var(--ios-label);
            font-size: 17px;
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 600;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* 平滑滚动 */
        .smooth-scroll {
            scroll-behavior: smooth;
        }
        
        /* 内容区域 */
        .ios-content-area {
            padding-top: 4px;
            padding-bottom: 130px;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }
        
        /* 底部固定按钮 */
        .ios-fixed-btn {
            background-color: var(--ios-blue);
            color: white;
            border-radius: 10px;
            font-weight: 500;
            font-size: 15px;
            padding: 12px 0;
            width: 100%;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,122,255,0.3);
        }
        
        /* iOS表单元素样式 */
        .ios-input {
            border-radius: 8px;
            border: 0.5px solid rgba(60,60,67,0.3);
            padding: 10px 12px;
            font-size: 15px;
            background-color: var(--ios-systemBackground);
            color: var(--ios-label);
            transition: all 0.2s ease;
        }
        
        .ios-input:focus {
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 1px rgba(0,122,255,0.3);
            outline: none;
        }
        
        .ios-select {
            position: relative;
            background-color: var(--ios-systemBackground);
        }
        
        .ios-select select {
            appearance: none;
            -webkit-appearance: none;
            border-radius: 8px;
            border: 0.5px solid rgba(60,60,67,0.3);
            padding: 10px 12px;
            padding-right: 30px;
            font-size: 15px;
            background-color: transparent;
            color: var(--ios-label);
            width: 100%;
            transition: all 0.2s ease;
        }
        
        .ios-select select:focus {
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 1px rgba(0,122,255,0.3);
            outline: none;
        }
        
        .ios-form-label {
            font-size: 15px;
            color: var(--ios-label);
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
        }
        
        /* iOS选择按钮样式 */
        .ios-selection-button {
            border-radius: 8px;
            border: 0.5px solid rgba(60,60,67,0.3);
            padding: 8px 0;
            font-size: 15px;
            text-align: center;
            background-color: var(--ios-systemBackground);
            color: var(--ios-label);
            transition: all 0.2s ease;
        }
        
        .ios-selection-button.selected {
            border-color: var(--ios-blue);
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
        }
        
        /* 图片上传样式 */
        .ios-upload-box {
            border-radius: 8px;
            border: 1.5px dashed rgba(60,60,67,0.3);
            background-color: rgba(60,60,67,0.03);
            aspect-ratio: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }
        
        .ios-upload-box:active {
            background-color: rgba(60,60,67,0.08);
        }
        
        /* 设施图标样式 */
        .facility-icon {
            width: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 设施菜单项悬停效果 */
        .facility-option {
            transition: background-color 0.2s ease, transform 0.1s ease;
        }
        
        .facility-option:hover {
            background-color: rgba(0,122,255,0.05);
        }
        
        .facility-option:active {
            transform: scale(0.96);
            background-color: rgba(0,122,255,0.1);
        }
        
        /* 设施项动画 */
        @keyframes slide-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slide-out {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(30px);
            }
        }
        
        .slide-in {
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.3s, transform 0.3s;
        }
        
        .slide-in.active {
            opacity: 1;
            transform: translateY(0);
        }
        
        .slide-out {
            animation: slide-out 0.3s forwards;
        }
        
        /* 设施菜单动画 */
        #facility-menu {
            transition: max-height 0.3s ease, opacity 0.2s ease;
            overflow: hidden;
        }
        
        /* 设施添加按钮 */
        #add-facility-btn {
            transition: background-color 0.2s ease;
        }
        
        #add-facility-btn:hover {
            background-color: rgba(242,242,247,0.8);
        }
        
        #facility-chevron {
            transition: transform 0.3s ease;
        }
        
        /* 底部动作表样式 */
        #facility-sheet {
            position: fixed;
            inset: 0;
            z-index: 50;
            background-color: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            display: flex;
            justify-content: center;
            align-items: flex-end;
            transition: opacity 0.3s ease;
            opacity: 1;
        }
        
        #facility-sheet.hidden {
            display: none;
            opacity: 0;
            pointer-events: none;
        }
        
        #facility-sheet-content {
            width: 100%;
            background-color: white;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            transform: translateY(100%);
            transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            padding-bottom: env(safe-area-inset-bottom);
            will-change: transform;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .facility-option {
            transition: background-color 0.2s ease, transform 0.1s ease;
        }
        
        .facility-option:active {
            transform: scale(0.98);
            background-color: rgba(0, 122, 255, 0.1);
        }
        
        /* 页面滚动锁定 */
        body.overflow-hidden {
            overflow: hidden;
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator">
            <div class="screen-title">发布房源</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="window.location.href='house-market.html'" class="ios-button ios-haptic">
                    <i class="fas fa-chevron-left text-[#007AFF] mr-1"></i>
                    <span class="text-[#007AFF]">返回</span>
                </button>
                <h1 class="text-center font-semibold">发布房源</h1>
                <div class="w-16"></div>
            </div>

            <div class="ios-content-area">
                <!-- 表单内容 -->
                <div class="p-4 space-y-4">
                    <!-- 图片上传 -->
                    <div class="bg-white rounded-lg p-4 ios-card ios-fade-in">
                        <h3 class="ios-section-title mb-3">房源图片</h3>
                        <div class="grid grid-cols-4 gap-2">
                            <div class="ios-upload-box ios-button ios-haptic">
                                <i class="fas fa-camera text-[#8E8E93] mb-1 text-xl"></i>
                                <span class="text-xs text-[#8E8E93]">封面图</span>
                            </div>
                            <div class="ios-upload-box ios-button ios-haptic">
                                <i class="fas fa-plus text-[#8E8E93] text-xl"></i>
                            </div>
                        </div>
                        <p class="text-xs text-[#8E8E93] mt-3">最多上传9张图片，建议上传客厅、卧室、厨房、卫生间等不同区域照片</p>
                    </div>

                    <!-- 基本信息 -->
                    <div class="bg-white rounded-lg p-4 ios-card ios-fade-in" style="animation-delay: 0.05s;">
                        <h3 class="ios-section-title mb-4">基本信息</h3>
                        
                        <!-- 房源标题 -->
                        <div class="mb-4">
                            <label class="ios-form-label">房源标题</label>
                            <input type="text" 
                                   placeholder="请输入房源标题（建议包含小区名、户型等信息）" 
                                   class="ios-input w-full">
                        </div>

                        <!-- 户型选择 -->
                        <div class="mb-4">
                            <label class="ios-form-label">户型</label>
                            <div class="grid grid-cols-3 gap-4">
                                <div class="ios-select">
                                    <select class="ios-input">
                                        <option value="">室</option>
                                        <option value="1">1室</option>
                                        <option value="2">2室</option>
                                        <option value="3">3室</option>
                                        <option value="4">4室</option>
                                        <option value="5">5室及以上</option>
                                    </select>
                                    <i class="fas fa-chevron-down absolute right-3 top-1/2 -translate-y-1/2 text-[#8E8E93] text-xs pointer-events-none"></i>
                                </div>
                                <div class="ios-select">
                                    <select class="ios-input">
                                        <option value="">厅</option>
                                        <option value="0">0厅</option>
                                        <option value="1">1厅</option>
                                        <option value="2">2厅</option>
                                        <option value="3">3厅及以上</option>
                                    </select>
                                    <i class="fas fa-chevron-down absolute right-3 top-1/2 -translate-y-1/2 text-[#8E8E93] text-xs pointer-events-none"></i>
                                </div>
                                <div class="ios-select">
                                    <select class="ios-input">
                                        <option value="">卫</option>
                                        <option value="0">0卫</option>
                                        <option value="1">1卫</option>
                                        <option value="2">2卫</option>
                                        <option value="3">3卫及以上</option>
                                    </select>
                                    <i class="fas fa-chevron-down absolute right-3 top-1/2 -translate-y-1/2 text-[#8E8E93] text-xs pointer-events-none"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 租金和押金 -->
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="ios-form-label">租金</label>
                                <div class="relative">
                                    <input type="number" 
                                           placeholder="请输入" 
                                           class="ios-input w-full pr-16">
                                    <span class="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-[#8E8E93]">元/月</span>
                                </div>
                            </div>
                            <div>
                                <label class="ios-form-label">押金</label>
                                <div class="relative">
                                    <input type="number" 
                                           placeholder="请输入" 
                                           class="ios-input w-full pr-16">
                                    <span class="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-[#8E8E93]">个月</span>
                                </div>
                            </div>
                        </div>

                        <!-- 付款方式 -->
                        <div class="mb-4">
                            <label class="ios-form-label">付款方式</label>
                            <div class="grid grid-cols-3 gap-2">
                                <button type="button" class="ios-selection-button selected ios-button ios-haptic">押一付一</button>
                                <button type="button" class="ios-selection-button ios-button ios-haptic">押一付三</button>
                                <button type="button" class="ios-selection-button ios-button ios-haptic">押一付六</button>
                                <button type="button" class="ios-selection-button ios-button ios-haptic">押二付一</button>
                                <button type="button" class="ios-selection-button ios-button ios-haptic">半年付</button>
                                <button type="button" class="ios-selection-button ios-button ios-haptic">年付</button>
                            </div>
                        </div>

                        <!-- 出租方式 -->
                        <div class="mb-4">
                            <label class="ios-form-label">出租方式</label>
                            <div class="grid grid-cols-2 gap-4">
                                <button type="button" class="ios-selection-button selected ios-button ios-haptic">整租</button>
                                <button type="button" class="ios-selection-button ios-button ios-haptic">合租</button>
                            </div>
                        </div>

                        <!-- 面积和楼层 -->
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="ios-form-label">房屋面积</label>
                                <div class="relative">
                                    <input type="number" 
                                           placeholder="请输入" 
                                           class="ios-input w-full pr-10">
                                    <span class="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-[#8E8E93]">㎡</span>
                                </div>
                            </div>
                            <div>
                                <label class="ios-form-label">楼层</label>
                                <div class="flex items-center space-x-1">
                                    <div class="relative flex-1">
                                        <input type="number" 
                                               placeholder="所在层" 
                                               class="ios-input w-full text-center">
                                    </div>
                                    <span class="text-[#8E8E93] text-sm">/</span>
                                    <div class="relative flex-1">
                                        <input type="number" 
                                               placeholder="总层" 
                                               class="ios-input w-full text-center">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 朝向和装修 -->
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="ios-form-label">朝向</label>
                                <div class="ios-select">
                                    <select class="ios-input">
                                        <option value="">请选择</option>
                                        <option value="南">南</option>
                                        <option value="北">北</option>
                                        <option value="东">东</option>
                                        <option value="西">西</option>
                                        <option value="东南">东南</option>
                                        <option value="西南">西南</option>
                                        <option value="东北">东北</option>
                                        <option value="西北">西北</option>
                                        <option value="南北通透">南北通透</option>
                                    </select>
                                    <i class="fas fa-chevron-down absolute right-3 top-1/2 -translate-y-1/2 text-[#8E8E93] text-xs pointer-events-none"></i>
                                </div>
                            </div>
                            <div>
                                <label class="ios-form-label">装修情况</label>
                                <div class="ios-select">
                                    <select class="ios-input">
                                        <option value="">请选择</option>
                                        <option value="精装修">精装修</option>
                                        <option value="简装修">简装修</option>
                                        <option value="毛坯">毛坯</option>
                                    </select>
                                    <i class="fas fa-chevron-down absolute right-3 top-1/2 -translate-y-1/2 text-[#8E8E93] text-xs pointer-events-none"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 房屋配置 -->
                    <div class="bg-white rounded-lg p-4 ios-card ios-fade-in" style="animation-delay: 0.1s;">
                        <h3 class="ios-section-title mb-3">房屋配置</h3>
                        <div class="grid grid-cols-4 gap-2">
                            <button class="ios-selection-button ios-button ios-haptic">床</button>
                            <button class="ios-selection-button ios-button ios-haptic">衣柜</button>
                            <button class="ios-selection-button ios-button ios-haptic">空调</button>
                            <button class="ios-selection-button ios-button ios-haptic">冰箱</button>
                            <button class="ios-selection-button ios-button ios-haptic">洗衣机</button>
                            <button class="ios-selection-button ios-button ios-haptic">热水器</button>
                            <button class="ios-selection-button ios-button ios-haptic">电视</button>
                            <button class="ios-selection-button ios-button ios-haptic">宽带</button>
                            <button class="ios-selection-button ios-button ios-haptic">沙发</button>
                            <button class="ios-selection-button ios-button ios-haptic">桌椅</button>
                            <button class="ios-selection-button ios-button ios-haptic">燃气灶</button>
                            <button class="ios-selection-button ios-button ios-haptic">阳台</button>
                        </div>
                    </div>

                    <!-- 特色标签 -->
                    <div class="bg-white rounded-lg p-4 ios-card ios-fade-in" style="animation-delay: 0.15s;">
                        <h3 class="ios-section-title mb-3">特色标签</h3>
                        <div class="grid grid-cols-3 gap-2">
                            <button class="ios-selection-button ios-button ios-haptic">近地铁</button>
                            <button class="ios-selection-button ios-button ios-haptic">拎包入住</button>
                            <button class="ios-selection-button ios-button ios-haptic">随时看房</button>
                            <button class="ios-selection-button ios-button ios-haptic">新上房源</button>
                            <button class="ios-selection-button ios-button ios-haptic">南北通透</button>
                            <button class="ios-selection-button ios-button ios-haptic">电梯房</button>
                            <button class="ios-selection-button ios-button ios-haptic">独立卫生间</button>
                            <button class="ios-selection-button ios-button ios-haptic">带阳台</button>
                            <button class="ios-selection-button ios-button ios-haptic">安静</button>
                        </div>
                    </div>

                    <!-- 入住信息 -->
                    <div class="bg-white rounded-lg p-4 ios-card ios-fade-in" style="animation-delay: 0.2s;">
                        <h3 class="ios-section-title mb-3">入住信息</h3>
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="ios-form-label">可入住日期</label>
                                <div class="relative">
                                    <input type="date" 
                                           placeholder="请选择日期" 
                                           class="ios-input w-full">
                                </div>
                            </div>
                            <div>
                                <label class="ios-form-label">最短租期</label>
                                <div class="ios-select">
                                    <select class="ios-input">
                                        <option value="">请选择</option>
                                        <option value="1">不限</option>
                                        <option value="3">3个月</option>
                                        <option value="6">6个月</option>
                                        <option value="12">1年</option>
                                        <option value="24">2年</option>
                                    </select>
                                    <i class="fas fa-chevron-down absolute right-3 top-1/2 -translate-y-1/2 text-[#8E8E93] text-xs pointer-events-none"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细描述 -->
                    <div class="bg-white rounded-lg p-4 ios-card ios-fade-in" style="animation-delay: 0.25s;">
                        <h3 class="ios-section-title mb-3">详细描述</h3>
                        <textarea 
                            placeholder="详细描述一下房源情况，例如周边配套设施、交通情况、室内装修等等"
                            class="ios-input w-full h-32 resize-none"></textarea>
                    </div>
                    
                    <!-- 位置信息 -->
                    <div class="bg-white rounded-lg p-4 ios-card ios-fade-in" style="animation-delay: 0.3s;">
                        <h3 class="ios-section-title mb-3">位置信息</h3>
                        
                        <!-- 地址选择 -->
                        <div class="mb-4">
                            <label class="ios-form-label">所在城市</label>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="ios-select">
                                    <select class="ios-input">
                                        <option value="">请选择省/直辖市</option>
                                        <option value="北京市">北京市</option>
                                        <option value="上海市">上海市</option>
                                        <option value="广东省">广东省</option>
                                        <option value="江苏省">江苏省</option>
                                        <option value="浙江省">浙江省</option>
                                    </select>
                                    <i class="fas fa-chevron-down absolute right-3 top-1/2 -translate-y-1/2 text-[#8E8E93] text-xs pointer-events-none"></i>
                                </div>
                                <div class="ios-select">
                                    <select class="ios-input">
                                        <option value="">请选择城市</option>
                                        <option value="北京市">北京市</option>
                                        <option value="上海市">上海市</option>
                                        <option value="广州市">广州市</option>
                                        <option value="深圳市">深圳市</option>
                                        <option value="杭州市">杭州市</option>
                                    </select>
                                    <i class="fas fa-chevron-down absolute right-3 top-1/2 -translate-y-1/2 text-[#8E8E93] text-xs pointer-events-none"></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 区域选择 -->
                        <div class="mb-4">
                            <label class="ios-form-label">所在区域</label>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="ios-select">
                                    <select class="ios-input">
                                        <option value="">请选择区/县</option>
                                        <option value="朝阳区">朝阳区</option>
                                        <option value="海淀区">海淀区</option>
                                        <option value="东城区">东城区</option>
                                        <option value="西城区">西城区</option>
                                        <option value="丰台区">丰台区</option>
                                    </select>
                                    <i class="fas fa-chevron-down absolute right-3 top-1/2 -translate-y-1/2 text-[#8E8E93] text-xs pointer-events-none"></i>
                                </div>
                                <div class="ios-select">
                                    <select class="ios-input">
                                        <option value="">请选择商圈</option>
                                        <option value="望京">望京</option>
                                        <option value="三里屯">三里屯</option>
                                        <option value="中关村">中关村</option>
                                        <option value="亚运村">亚运村</option>
                                        <option value="国贸">国贸</option>
                                    </select>
                                    <i class="fas fa-chevron-down absolute right-3 top-1/2 -translate-y-1/2 text-[#8E8E93] text-xs pointer-events-none"></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 小区信息 -->
                        <div class="mb-4">
                            <label class="ios-form-label">小区名称</label>
                            <div class="flex items-center space-x-2">
                                <input type="text" 
                                    placeholder="请输入小区名称" 
                                    class="ios-input w-full">
                                <button class="ios-button ios-haptic px-3 py-2 bg-[#F2F2F7] rounded-lg">
                                    <i class="fas fa-search text-[#8E8E93]"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 详细地址 -->
                        <div class="mb-4">
                            <label class="ios-form-label">详细地址</label>
                            <div class="grid grid-cols-2 gap-4 mb-2">
                                <div>
                                    <input type="text" 
                                        placeholder="楼栋号" 
                                        class="ios-input w-full">
                                </div>
                                <div>
                                    <input type="text" 
                                        placeholder="单元号" 
                                        class="ios-input w-full">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <input type="text" 
                                        placeholder="门牌号" 
                                        class="ios-input w-full">
                                </div>
                                <div class="flex items-center justify-center">
                                    <span class="text-[#8E8E93] text-sm">（仅房东可见）</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 地图选点 -->
                        <div class="mb-4">
                            <label class="ios-form-label">地图定位</label>
                            <div class="h-60 bg-[#F2F2F7] rounded-lg relative overflow-hidden" id="map-container">
                                <!-- 高德地图容器 -->
                                <div id="amap-container" class="w-full h-full"></div>
                                
                                <!-- 交互控件 -->
                                <div class="absolute top-2 right-2 z-10 flex flex-col space-y-2">
                                    <button id="zoom-in-btn" class="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center ios-button ios-haptic">
                                        <i class="fas fa-plus text-[#007AFF]"></i>
                                    </button>
                                    <button id="zoom-out-btn" class="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center ios-button ios-haptic">
                                        <i class="fas fa-minus text-[#007AFF]"></i>
                                    </button>
                                    <button id="location-btn" class="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center ios-button ios-haptic">
                                        <i class="fas fa-location-arrow text-[#007AFF]"></i>
                                    </button>
                                </div>
                                
                                <!-- 搜索框 -->
                                <div class="absolute top-2 left-2 right-12 z-10">
                                    <div class="bg-white rounded-lg shadow-md flex items-center px-3 py-2">
                                        <i class="fas fa-search text-[#8E8E93] mr-2"></i>
                                        <input type="text" placeholder="搜索地址" class="bg-transparent border-none outline-none text-sm w-full" id="map-search">
                                    </div>
                                </div>
                                
                                <!-- 位置信息显示 -->
                                <div class="absolute bottom-2 left-2 right-2 bg-white bg-opacity-90 backdrop-filter backdrop-blur-sm rounded-lg shadow-md px-3 py-2 z-10">
                                    <p class="text-sm font-medium" id="selected-address">北京市朝阳区望京</p>
                                    <p class="text-xs text-[#8E8E93]">拖动地图或标记以调整位置</p>
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-[#8E8E93]">经纬度: <span id="selected-coords">116.4877, 39.9890</span></span>
                                        <button id="confirm-location-btn" class="ios-button ios-haptic text-[#007AFF] text-sm font-medium">确认位置</button>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-[#8E8E93] mt-2">请在地图上选择房源的精确位置，以便租客可以准确查看周边环境。</p>
                        </div>
                        
                        <!-- 周边设施 -->
                        <div>
                            <label class="ios-form-label mt-4">周边设施距离</label>
                            <div id="facilities-container">
                                <!-- 地铁站设施项 -->
                                <div class="facility-item grid grid-cols-2 gap-4 mb-2 slide-in active">
                                    <div class="flex items-center space-x-2">
                                        <div class="facility-icon">
                                            <i class="fas fa-subway text-[#007AFF] w-5"></i>
                                        </div>
                                        <div class="ios-select flex-1">
                                            <select class="ios-input facility-select">
                                                <option value="">选择地铁站</option>
                                                <option value="望京站">望京站</option>
                                                <option value="望京南站">望京南站</option>
                                                <option value="望京西站">望京西站</option>
                                                <option value="东直门站">东直门站</option>
                                                <option value="西直门站">西直门站</option>
                                                <option value="国贸站">国贸站</option>
                                            </select>
                                            <i class="fas fa-chevron-down absolute right-3 top-1/2 -translate-y-1/2 text-[#8E8E93] text-xs pointer-events-none"></i>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="relative flex-1">
                                            <input type="number" 
                                                placeholder="距离" 
                                                class="ios-input w-full pr-12 facility-distance">
                                            <span class="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-[#8E8E93]">米</span>
                                        </div>
                                        <button class="delete-facility ios-button ios-haptic ml-2 w-8 h-8 bg-[#F2F2F7] rounded-full flex items-center justify-center">
                                            <i class="fas fa-times text-[#8E8E93]"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 购物中心设施项 -->
                                <div class="facility-item grid grid-cols-2 gap-4 mb-2 slide-in active">
                                    <div class="flex items-center space-x-2">
                                        <div class="facility-icon">
                                            <i class="fas fa-shopping-bag text-[#007AFF] w-5"></i>
                                        </div>
                                        <div class="ios-select flex-1">
                                            <select class="ios-input facility-select">
                                                <option value="">选择购物中心</option>
                                                <option value="永辉超市">永辉超市</option>
                                                <option value="华联商厦">华联商厦</option>
                                                <option value="万达广场">万达广场</option>
                                                <option value="西单商场">西单商场</option>
                                                <option value="三里屯太古里">三里屯太古里</option>
                                            </select>
                                            <i class="fas fa-chevron-down absolute right-3 top-1/2 -translate-y-1/2 text-[#8E8E93] text-xs pointer-events-none"></i>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="relative flex-1">
                                            <input type="number" 
                                                placeholder="距离" 
                                                class="ios-input w-full pr-12 facility-distance">
                                            <span class="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-[#8E8E93]">米</span>
                                        </div>
                                        <button class="delete-facility ios-button ios-haptic ml-2 w-8 h-8 bg-[#F2F2F7] rounded-full flex items-center justify-center">
                                            <i class="fas fa-times text-[#8E8E93]"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 添加更多设施按钮 -->
                            <div class="mt-3">
                                <button id="add-facility-btn" class="ios-button ios-haptic flex items-center justify-center p-3 w-full border border-dashed border-[#8E8E93] rounded-lg bg-[#FAFAFA]">
                                    <i class="fas fa-plus text-[#007AFF] mr-2"></i>
                                    <span class="text-[#007AFF]">添加周边设施</span>
                                </button>
                            </div>
                            
                            <!-- iOS风格的动作表弹出层 -->
                            <div id="facility-sheet" class="fixed inset-0 bg-black bg-opacity-50" style="z-index: 100; display: none;">
                                <div id="facility-sheet-content" class="absolute bottom-0 left-0 right-0 bg-white rounded-t-xl transform translate-y-full" style="transition: transform 0.3s ease; will-change: transform;">
                                    <div class="p-4 ios-bottom-nav">
                                        <!-- 标题和关闭按钮 -->
                                        <div class="flex justify-between items-center mb-4">
                                            <h3 class="text-xl font-semibold">选择设施类型</h3>
                                            <button id="close-facility-sheet" class="ios-button ios-haptic w-8 h-8 rounded-full flex items-center justify-center bg-[#F2F2F7]">
                                                <i class="fas fa-times text-[#8E8E93]"></i>
                                            </button>
                                        </div>
                                        
                                        <!-- 设施类型网格 -->
                                        <div class="grid grid-cols-3 gap-3 mb-4">
                                            <div class="facility-option ios-button ios-haptic flex flex-col items-center p-3 rounded-xl" data-icon="fas fa-subway" data-type="地铁站" data-options='["望京站", "望京南站", "望京西站", "东直门站", "西直门站", "国贸站"]'>
                                                <div class="w-12 h-12 rounded-full bg-[rgba(0,122,255,0.1)] flex items-center justify-center mb-2">
                                                    <i class="fas fa-subway text-[#007AFF] text-xl"></i>
                                                </div>
                                                <span class="text-sm">地铁站</span>
                                            </div>
                                            <div class="facility-option ios-button ios-haptic flex flex-col items-center p-3 rounded-xl" data-icon="fas fa-shopping-bag" data-type="购物中心" data-options='["永辉超市", "华联商厦", "万达广场", "西单商场", "三里屯太古里"]'>
                                                <div class="w-12 h-12 rounded-full bg-[rgba(0,122,255,0.1)] flex items-center justify-center mb-2">
                                                    <i class="fas fa-shopping-bag text-[#007AFF] text-xl"></i>
                                                </div>
                                                <span class="text-sm">购物中心</span>
                                            </div>
                                            <div class="facility-option ios-button ios-haptic flex flex-col items-center p-3 rounded-xl" data-icon="fas fa-utensils" data-type="餐饮" data-options='["肯德基", "麦当劳", "星巴克", "必胜客", "海底捞"]'>
                                                <div class="w-12 h-12 rounded-full bg-[rgba(0,122,255,0.1)] flex items-center justify-center mb-2">
                                                    <i class="fas fa-utensils text-[#007AFF] text-xl"></i>
                                                </div>
                                                <span class="text-sm">餐饮</span>
                                            </div>
                                            <div class="facility-option ios-button ios-haptic flex flex-col items-center p-3 rounded-xl" data-icon="fas fa-graduation-cap" data-type="学校" data-options='["小学", "中学", "高中", "大学", "幼儿园"]'>
                                                <div class="w-12 h-12 rounded-full bg-[rgba(0,122,255,0.1)] flex items-center justify-center mb-2">
                                                    <i class="fas fa-graduation-cap text-[#007AFF] text-xl"></i>
                                                </div>
                                                <span class="text-sm">学校</span>
                                            </div>
                                            <div class="facility-option ios-button ios-haptic flex flex-col items-center p-3 rounded-xl" data-icon="fas fa-hospital" data-type="医疗" data-options='["社区医院", "三甲医院", "诊所", "药店"]'>
                                                <div class="w-12 h-12 rounded-full bg-[rgba(0,122,255,0.1)] flex items-center justify-center mb-2">
                                                    <i class="fas fa-hospital text-[#007AFF] text-xl"></i>
                                                </div>
                                                <span class="text-sm">医疗</span>
                                            </div>
                                            <div class="facility-option ios-button ios-haptic flex flex-col items-center p-3 rounded-xl" data-icon="fas fa-bus" data-type="公交站" data-options='["公交站"]'>
                                                <div class="w-12 h-12 rounded-full bg-[rgba(0,122,255,0.1)] flex items-center justify-center mb-2">
                                                    <i class="fas fa-bus text-[#007AFF] text-xl"></i>
                                                </div>
                                                <span class="text-sm">公交站</span>
                                            </div>
                                            <div class="facility-option ios-button ios-haptic flex flex-col items-center p-3 rounded-xl" data-icon="fas fa-tree" data-type="公园" data-options='["社区公园", "城市公园", "广场"]'>
                                                <div class="w-12 h-12 rounded-full bg-[rgba(0,122,255,0.1)] flex items-center justify-center mb-2">
                                                    <i class="fas fa-tree text-[#007AFF] text-xl"></i>
                                                </div>
                                                <span class="text-sm">公园</span>
                                            </div>
                                            <div class="facility-option ios-button ios-haptic flex flex-col items-center p-3 rounded-xl" data-icon="fas fa-home" data-type="小区设施" data-options='["游泳池", "健身房", "儿童乐园", "篮球场", "网球场"]'>
                                                <div class="w-12 h-12 rounded-full bg-[rgba(0,122,255,0.1)] flex items-center justify-center mb-2">
                                                    <i class="fas fa-home text-[#007AFF] text-xl"></i>
                                                </div>
                                                <span class="text-sm">小区设施</span>
                                            </div>
                                            <div class="facility-option ios-button ios-haptic flex flex-col items-center p-3 rounded-xl" data-icon="fas fa-car" data-type="停车场" data-options='["地下停车场", "路边停车位", "专用车位"]'>
                                                <div class="w-12 h-12 rounded-full bg-[rgba(0,122,255,0.1)] flex items-center justify-center mb-2">
                                                    <i class="fas fa-car text-[#007AFF] text-xl"></i>
                                                </div>
                                                <span class="text-sm">停车场</span>
                                            </div>
                                        </div>
                                        
                                        <!-- 取消按钮 -->
                                        <button id="cancel-facility-btn" class="ios-button ios-haptic w-full py-3 mt-2 bg-[#F2F2F7] rounded-xl text-[#007AFF] font-medium">
                                            取消
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 联系信息 -->
                    <div class="bg-white rounded-lg p-4 ios-card ios-fade-in" style="animation-delay: 0.35s;">
                        <h3 class="ios-section-title mb-3">联系信息</h3>
                        
                        <!-- 联系人 -->
                        <div class="mb-4">
                            <label class="ios-form-label">联系人</label>
                            <input type="text" 
                                   placeholder="请输入联系人姓名" 
                                   class="ios-input w-full">
                        </div>
                        
                        <!-- 联系电话 -->
                        <div class="mb-4">
                            <label class="ios-form-label">联系电话</label>
                            <div class="flex items-center space-x-2">
                                <input type="tel" 
                                       placeholder="请输入联系电话" 
                                       class="ios-input w-full">
                                <button class="ios-button ios-haptic px-3 py-2 bg-[#F2F2F7] rounded-lg">
                                    <i class="fas fa-address-book text-[#8E8E93]"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 微信号 -->
                        <div class="mb-4">
                            <label class="ios-form-label">微信号（选填）</label>
                            <input type="text" 
                                   placeholder="请输入微信号" 
                                   class="ios-input w-full">
                        </div>
                        
                        <!-- 看房时间 -->
                        <div class="mb-4">
                            <label class="ios-form-label">看房时间</label>
                            <div class="grid grid-cols-3 gap-2">
                                <button type="button" class="ios-selection-button selected ios-button ios-haptic">随时可看</button>
                                <button type="button" class="ios-selection-button ios-button ios-haptic">工作日</button>
                                <button type="button" class="ios-selection-button ios-button ios-haptic">周末</button>
                                <button type="button" class="ios-selection-button ios-button ios-haptic">上午</button>
                                <button type="button" class="ios-selection-button ios-button ios-haptic">下午</button>
                                <button type="button" class="ios-selection-button ios-button ios-haptic">晚上</button>
                            </div>
                        </div>
                        
                        <!-- 提示信息 -->
                        <div class="flex items-start mt-4">
                            <i class="fas fa-info-circle text-[#8E8E93] mt-0.5 mr-2"></i>
                            <p class="text-xs text-[#8E8E93]">您的联系信息将用于房源发布及沟通，我们会保护您的隐私安全。</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部固定按钮 -->
            <div class="fixed bottom-0 left-0 right-0 p-4 ios-bottom-nav z-40">
                <button class="ios-fixed-btn ios-button ios-haptic">发布房源</button>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载后执行的脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 调试信息
            window.debugMode = true;
            console.log('页面加载完成，开始初始化...');
            
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 选择按钮切换状态
            const selectionButtons = document.querySelectorAll('.ios-selection-button');
            selectionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const parent = this.parentElement;
                    const isSingleSelect = parent.childElementCount === 2; // 如果只有两个按钮，则为单选
                    
                    if (isSingleSelect) {
                        parent.querySelectorAll('.ios-selection-button').forEach(btn => {
                            btn.classList.remove('selected');
                        });
                        this.classList.add('selected');
                    } else {
                        this.classList.toggle('selected');
                    }
                });
            });
            
            // 高德地图初始化与交互
            const mapContainer = document.getElementById('map-container');
            const selectedAddress = document.getElementById('selected-address');
            const selectedCoords = document.getElementById('selected-coords');
            const mapSearch = document.getElementById('map-search');
            const confirmLocationBtn = document.getElementById('confirm-location-btn');
            
            if (mapContainer && document.getElementById('amap-container')) {
                // 初始化地图
                let map = new AMap.Map('amap-container', {
                    zoom: 15,  // 地图缩放级别
                    center: [116.4877, 39.9890], // 初始中心点坐标 (经度, 纬度)
                    resizeEnable: true, // 自动调整大小
                });
                
                // 添加标记
                let marker = new AMap.Marker({
                    position: map.getCenter(),
                    draggable: true, // 可拖动
                    cursor: 'move',
                    animation: 'AMAP_ANIMATION_DROP', // 添加动画效果
                    title: '拖动我调整位置'
                });
                marker.setMap(map);
                
                // 添加地图控件
                map.plugin(['AMap.ToolBar', 'AMap.Scale'], function() {
                    // 地图工具条
                    let toolbar = new AMap.ToolBar({
                        position: 'RB', // 右下角
                        offset: new AMap.Pixel(15, 60), // 偏移量
                        liteStyle: true // 简洁模式
                    });
                    map.addControl(toolbar);
                    
                    // 比例尺
                    let scale = new AMap.Scale({
                        position: 'LB', // 左下角
                        offset: new AMap.Pixel(15, 15) // 偏移量
                    });
                    map.addControl(scale);
                });
                
                // 地理编码服务
                let geocoder = new AMap.Geocoder({
                    city: "010", // 城市，默认："全国"
                    radius: 1000 // 范围，默认：500
                });
                
                // 逆地理编码，获取地址信息
                function geocode(lnglat) {
                    geocoder.getAddress(lnglat, function(status, result) {
                        if (status === 'complete' && result.info === 'OK') {
                            // 获取地址
                            let address = result.regeocode.formattedAddress;
                            selectedAddress.textContent = address;
                            selectedCoords.textContent = lnglat.join(', ');
                            
                            // 振动反馈
                            if ('vibrate' in navigator) {
                                navigator.vibrate(10);
                            }
                        } else {
                            console.error('获取地址失败');
                        }
                    });
                }
                
                // 地图点击事件
                map.on('click', function(e) {
                    const lnglat = [e.lnglat.getLng(), e.lnglat.getLat()];
                    marker.setPosition(lnglat);
                    geocode(lnglat);
                });
                
                // 标记拖动结束事件
                marker.on('dragend', function(e) {
                    const lnglat = [e.lnglat.getLng(), e.lnglat.getLat()];
                    geocode(lnglat);
                });
                
                // 自定义缩放按钮事件
                document.getElementById('zoom-in-btn').addEventListener('click', function() {
                    map.zoomIn();
                });
                
                document.getElementById('zoom-out-btn').addEventListener('click', function() {
                    map.zoomOut();
                });
                
                // 定位按钮事件
                document.getElementById('location-btn').addEventListener('click', function() {
                    map.plugin('AMap.Geolocation', function() {
                        let geolocation = new AMap.Geolocation({
                            enableHighAccuracy: true, // 是否使用高精度定位，默认:true
                            timeout: 10000, // 超过10秒后停止定位，默认：无穷大
                            maximumAge: 0, // 定位结果缓存0毫秒，默认：0
                            convert: true, // 自动偏移坐标，偏移后的坐标为高德坐标，默认：true
                            showButton: false, // 显示定位按钮，默认：true
                            buttonPosition: 'LB', // 定位按钮停靠位置，默认：'LB'，左下角
                            buttonOffset: new AMap.Pixel(10, 20), // 定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
                            showMarker: false, // 定位成功后在定位到的位置显示点标记，默认：true
                            showCircle: false, // 定位成功后用圆圈表示定位精度范围，默认：true
                            panToLocation: true, // 定位成功后将定位到的位置作为地图中心点，默认：true
                            zoomToAccuracy: true // 定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
                        });
                        map.addControl(geolocation);
                        
                        geolocation.getCurrentPosition(function(status, result) {
                            if (status === 'complete') {
                                // 定位成功
                                const lnglat = [result.position.lng, result.position.lat];
                                marker.setPosition(lnglat);
                                geocode(lnglat);
                                
                                // 反馈成功
                                if ('vibrate' in navigator) {
                                    navigator.vibrate([10, 50, 10]);
                                }
                            } else {
                                // 定位失败
                                console.error('定位失败', result.message);
                                selectedAddress.textContent = '定位失败，请手动选择位置';
                            }
                        });
                    });
                });
                
                // 搜索框交互
                mapSearch.addEventListener('focus', function() {
                    this.parentElement.style.boxShadow = '0 0 0 2px rgba(0,122,255,0.3)';
                });
                
                mapSearch.addEventListener('blur', function() {
                    this.parentElement.style.boxShadow = '';
                });
                
                // 搜索功能
                mapSearch.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        const keyword = this.value.trim();
                        if (keyword) {
                            // 使用高德地图搜索服务
                            AMap.plugin('AMap.PlaceSearch', function() {
                                let placeSearch = new AMap.PlaceSearch({
                                    city: '010', // 城市
                                    pageSize: 1, // 单页显示结果数,默认10
                                    pageIndex: 1 // 页码,默认1
                                });
                                
                                placeSearch.search(keyword, function(status, result) {
                                    if (status === 'complete' && result.info === 'OK') {
                                        // 搜索成功
                                        const poi = result.poiList.pois[0];
                                        if (poi) {
                                            // 获取POI的经纬度
                                            const lnglat = [poi.location.lng, poi.location.lat];
                                            
                                            // 更新地图中心和标记位置
                                            map.setCenter(lnglat);
                                            marker.setPosition(lnglat);
                                            
                                            // 更新地址信息
                                            selectedAddress.textContent = poi.name + '（' + poi.address + '）';
                                            selectedCoords.textContent = lnglat.join(', ');
                                            
                                            // 视觉反馈
                                            marker.setAnimation('AMAP_ANIMATION_BOUNCE');
                                            setTimeout(function() {
                                                marker.setAnimation(null);
                                            }, 2000);
                                            
                                            // 振动反馈
                                            if ('vibrate' in navigator) {
                                                navigator.vibrate(20);
                                            }
                                        } else {
                                            selectedAddress.textContent = '未找到该地点，请尝试其他关键词';
                                        }
                                    } else {
                                        selectedAddress.textContent = '搜索失败，请尝试其他关键词';
                                    }
                                });
                            });
                        }
                    }
                });
                
                // 确认位置按钮
                confirmLocationBtn.addEventListener('click', function() {
                    // 获取当前标记位置
                    const position = marker.getPosition();
                    const lnglat = [position.getLng(), position.getLat()];
                    
                    // 这里可以添加确认位置后的操作，例如将坐标值保存到表单中
                    console.log('确认位置：', selectedAddress.textContent, lnglat);
                    
                    // 视觉反馈
                    this.textContent = '已确认';
                    setTimeout(() => {
                        this.textContent = '确认位置';
                    }, 2000);
                    
                    // 振动反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate([10, 30, 10]);
                    }
                });
            }
            
            // 直接定义弹出层操作的函数
            // 周边设施动态添加功能
            const facilitiesContainer = document.getElementById('facilities-container');
            const addFacilityBtn = document.getElementById('add-facility-btn');
            const facilitySheet = document.getElementById('facility-sheet');
            const facilitySheetContent = document.getElementById('facility-sheet-content');
            const closeFacilitySheet = document.getElementById('close-facility-sheet');
            const cancelFacilityBtn = document.getElementById('cancel-facility-btn');
            
            // 调试信息
            if (window.debugMode) {
                console.log('设施DOM元素状态:');
                console.log('- facilitiesContainer:', facilitiesContainer ? '存在' : '不存在');
                console.log('- addFacilityBtn:', addFacilityBtn ? '存在' : '不存在');
                console.log('- facilitySheet:', facilitySheet ? '存在' : '不存在');
                console.log('- facilitySheetContent:', facilitySheetContent ? '存在' : '不存在');
                console.log('- closeFacilitySheet:', closeFacilitySheet ? '存在' : '不存在');
                console.log('- cancelFacilityBtn:', cancelFacilityBtn ? '存在' : '不存在');
            }
            
            // 确保弹出层初始状态是正确的
            if (facilitySheet && facilitySheetContent) {
                facilitySheet.classList.add('hidden');
                facilitySheetContent.style.transform = 'translateY(100%)';
                if (window.debugMode) console.log('初始化弹出层状态');
            }
            
            // 定义一个显示弹出层的函数
            function showFacilitySheet() {
                if (window.debugMode) console.log('显示设施弹出层...');
                
                // 设置初始位置，确保从底部开始弹出
                facilitySheetContent.style.transform = 'translateY(100%)';
                
                // 移除隐藏类
                facilitySheet.classList.remove('hidden');
                
                // 强制重排
                void facilitySheetContent.offsetWidth;
                
                // 确保在动画开始前facilitySheet是可见的
                setTimeout(() => {
                    if (window.debugMode) console.log('应用弹出层动画...');
                    facilitySheetContent.style.transform = 'translateY(0)';
                    document.body.classList.add('overflow-hidden');
                }, 50);
            }
            
            // 定义一个隐藏弹出层的函数
            function hideFacilitySheet() {
                if (window.debugMode) console.log('隐藏设施弹出层...');
                
                // 添加退出动画
                facilitySheetContent.style.transform = 'translateY(100%)';
                
                // 等待动画完成后隐藏
                setTimeout(() => {
                    facilitySheet.classList.add('hidden');
                    document.body.classList.remove('overflow-hidden');
                    if (window.debugMode) console.log('弹出层现在应该是隐藏的');
                }, 300);
                
                // 振动反馈
                if ('vibrate' in navigator) {
                    navigator.vibrate(10);
                }
            }
            
            // 添加设施按钮点击事件 - 显示底部弹出层
            if (addFacilityBtn && facilitySheet) {
                addFacilityBtn.addEventListener('click', function() {
                    if (window.debugMode) console.log('添加设施按钮被点击');
                    showFacilitySheet();
                    
                    // 振动反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10);
                    }
                });
                
                // 绑定关闭事件
                if (closeFacilitySheet) {
                    closeFacilitySheet.addEventListener('click', hideFacilitySheet);
                }
                
                if (cancelFacilityBtn) {
                    cancelFacilityBtn.addEventListener('click', hideFacilitySheet);
                }
                
                // 点击背景关闭
                facilitySheet.addEventListener('click', function(e) {
                    if (e.target === this) {
                        hideFacilitySheet();
                    }
                });
                
                // 设施选项点击事件
                const facilityOptions = document.querySelectorAll('.facility-option');
                facilityOptions.forEach(option => {
                    option.addEventListener('click', function() {
                        if (window.debugMode) console.log('设施选项被点击:', this.querySelector('span').textContent);
                        
                        // 获取设施类型数据
                        const icon = this.getAttribute('data-icon');
                        const type = this.getAttribute('data-type');
                        const optionsStr = this.getAttribute('data-options');
                        const options = JSON.parse(optionsStr);
                        
                        // 创建新的设施项
                        addFacilityItem(icon, type, options);
                        
                        // 设施选中反馈
                        this.classList.add('bg-[rgba(0,122,255,0.1)]');
                        setTimeout(() => {
                            this.classList.remove('bg-[rgba(0,122,255,0.1)]');
                        }, 300);
                        
                        // 关闭弹出层
                        hideFacilitySheet();
                        
                        // 振动反馈
                        if ('vibrate' in navigator) {
                            navigator.vibrate(10);
                        }
                    });
                });
                
                // 为初始设施项绑定删除事件
                document.querySelectorAll('.delete-facility').forEach(btn => {
                    bindDeleteFacility(btn);
                });
            }
            
            // 添加新的设施项
            function addFacilityItem(iconClass, facilityType, options) {
                // 创建设施项容器
                const facilityItem = document.createElement('div');
                facilityItem.className = 'facility-item grid grid-cols-2 gap-4 mb-2 slide-in';
                
                // 创建选择部分
                const selectPart = document.createElement('div');
                selectPart.className = 'flex items-center space-x-2';
                
                const iconContainer = document.createElement('div');
                iconContainer.className = 'facility-icon';
                
                const icon = document.createElement('i');
                icon.className = `${iconClass} text-[#007AFF] w-5`;
                
                const selectContainer = document.createElement('div');
                selectContainer.className = 'ios-select flex-1';
                
                const select = document.createElement('select');
                select.className = 'ios-input facility-select';
                
                // 添加默认选项
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = `选择${facilityType}`;
                select.appendChild(defaultOption);
                
                // 添加其他选项
                options.forEach(optionText => {
                    const option = document.createElement('option');
                    option.value = optionText;
                    option.textContent = optionText;
                    select.appendChild(option);
                });
                
                const chevron = document.createElement('i');
                chevron.className = 'fas fa-chevron-down absolute right-3 top-1/2 -translate-y-1/2 text-[#8E8E93] text-xs pointer-events-none';
                
                // 创建距离输入部分
                const distancePart = document.createElement('div');
                distancePart.className = 'flex items-center';
                
                const inputContainer = document.createElement('div');
                inputContainer.className = 'relative flex-1';
                
                const input = document.createElement('input');
                input.type = 'number';
                input.placeholder = '距离';
                input.className = 'ios-input w-full pr-12 facility-distance';
                
                const unit = document.createElement('span');
                unit.className = 'absolute right-3 top-1/2 -translate-y-1/2 text-sm text-[#8E8E93]';
                unit.textContent = '米';
                
                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'delete-facility ios-button ios-haptic ml-2 w-8 h-8 bg-[#F2F2F7] rounded-full flex items-center justify-center';
                
                const deleteIcon = document.createElement('i');
                deleteIcon.className = 'fas fa-times text-[#8E8E93]';
                
                // 组装DOM结构
                iconContainer.appendChild(icon);
                selectContainer.appendChild(select);
                selectContainer.appendChild(chevron);
                
                selectPart.appendChild(iconContainer);
                selectPart.appendChild(selectContainer);
                
                inputContainer.appendChild(input);
                inputContainer.appendChild(unit);
                
                deleteBtn.appendChild(deleteIcon);
                
                distancePart.appendChild(inputContainer);
                distancePart.appendChild(deleteBtn);
                
                facilityItem.appendChild(selectPart);
                facilityItem.appendChild(distancePart);
                
                // 添加到容器
                facilitiesContainer.appendChild(facilityItem);
                
                // 绑定删除事件
                bindDeleteFacility(deleteBtn);
                
                // 添加进入动画
                setTimeout(() => {
                    facilityItem.classList.add('active');
                }, 10);
            }
            
            // 绑定删除设施事件
            function bindDeleteFacility(btn) {
                btn.addEventListener('click', function() {
                    const facilityItem = this.closest('.facility-item');
                    
                    // 添加删除动画
                    facilityItem.classList.add('slide-out');
                    
                    // 等待动画完成后删除元素
                    setTimeout(() => {
                        facilityItem.remove();
                    }, 300);
                    
                    // 振动反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate([10, 30, 10]);
                    }
                });
            }
        });
    </script>
    
    <!-- 高德地图API -->
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=YOUR_AMAP_KEY"></script>
    
    <!-- 弹出层修复脚本 - 使用最简单的方式 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM已加载，初始化弹出层功能');
            
            // 获取所有需要的元素
            const addBtn = document.getElementById('add-facility-btn');
            const sheet = document.getElementById('facility-sheet');
            const sheetContent = document.getElementById('facility-sheet-content');
            const closeBtn = document.getElementById('close-facility-sheet');
            const cancelBtn = document.getElementById('cancel-facility-btn');
            const facilityOptions = document.querySelectorAll('.facility-option');
            
            // 打印调试信息
            console.log('addBtn:', addBtn);
            console.log('sheet:', sheet);
            console.log('sheetContent:', sheetContent);
            
            // 确保所有需要的元素都存在
            if (!addBtn || !sheet || !sheetContent) {
                console.error('未找到必要的DOM元素，弹出层功能将不可用');
                return;
            }
            
            // 打开弹出层函数
            function showSheet() {
                console.log('打开设施选择弹出层');
                // 先显示背景层
                sheet.style.display = 'block';
                
                // 强制重排
                void sheetContent.offsetHeight;
                
                // 动画显示内容
                setTimeout(function() {
                    sheetContent.style.transform = 'translateY(0)';
                }, 10);
                
                // 锁定背景滚动
                document.body.style.overflow = 'hidden';
            }
            
            // 关闭弹出层函数
            function hideSheet() {
                console.log('关闭设施选择弹出层');
                // 先做退出动画
                sheetContent.style.transform = 'translateY(100%)';
                
                // 动画结束后隐藏整个层
                setTimeout(function() {
                    sheet.style.display = 'none';
                    document.body.style.overflow = '';
                }, 300);
            }
            
            // 绑定添加按钮点击事件
            addBtn.addEventListener('click', function(e) {
                console.log('添加设施按钮被点击');
                e.preventDefault();
                e.stopPropagation();
                showSheet();
            });
            
            // 绑定关闭按钮事件
            if (closeBtn) {
                closeBtn.addEventListener('click', hideSheet);
            }
            
            // 绑定取消按钮事件
            if (cancelBtn) {
                cancelBtn.addEventListener('click', hideSheet);
            }
            
            // 点击背景关闭
            sheet.addEventListener('click', function(e) {
                if (e.target === this) {
                    hideSheet();
                }
            });
            
            // 绑定设施选项点击事件
            facilityOptions.forEach(function(option) {
                option.addEventListener('click', function() {
                    console.log('选择了设施:', this.getAttribute('data-type'));
                    // 这里可以添加选择设施后的逻辑
                    hideSheet();
                });
            });
            
            // 提供全局调试函数
            window.showFacilitySheet = showSheet;
            window.hideFacilitySheet = hideSheet;
            
            console.log('弹出层功能初始化完成');
        });
    </script>
</body>
</html> 