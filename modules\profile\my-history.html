<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 浏览历史</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 600;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS日期分组标题 */
        .ios-section-header {
            font-size: 14px;
            font-weight: 600;
            color: var(--ios-secondaryLabel);
            padding: 4px 16px;
            margin: 16px 0 8px;
            letter-spacing: 0.01em;
            text-transform: uppercase;
        }
        
        /* iOS历史记录项目 */
        .ios-history-item {
            display: flex;
            padding: 12px 16px;
            background-color: white;
            margin-bottom: 1px;
            transition: transform 0.2s ease;
            align-items: center;
        }
        
        .ios-history-item:active {
            transform: translateX(4px);
            background-color: rgba(0,0,0,0.02);
        }
        
        .ios-history-item:first-child {
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
        }
        
        .ios-history-item:last-child {
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
            margin-bottom: 8px;
        }
        
        .ios-history-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            overflow: hidden;
            background-color: var(--ios-secondarySystemBackground);
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .ios-history-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .ios-history-title {
            font-weight: 500;
            font-size: 15px;
            line-height: 1.3;
            margin-bottom: 4px;
        }
        
        .ios-history-desc {
            font-size: 13px;
            color: var(--ios-secondaryLabel);
            margin-bottom: 4px;
        }
        
        .ios-history-time {
            font-size: 12px;
            color: var(--ios-tertiaryLabel);
        }
        
        .ios-chevron {
            color: var(--ios-tertiaryLabel);
            margin-left: 12px;
            font-size: 12px;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* iOS清除按钮 */
        .ios-clear-button {
            background-color: var(--ios-blue);
            color: white;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 500;
            font-size: 15px;
            margin: 20px auto;
            display: block;
            width: calc(100% - 32px);
            text-align: center;
        }
        
        .ios-clear-button:active {
            background-color: rgba(0, 122, 255, 0.8);
            transform: scale(0.98);
        }
        
        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }
        
        /* iOS标签样式 */
        .ios-tab-bar {
            display: flex;
            background-color: var(--ios-systemBackground);
            border-bottom: 0.5px solid rgba(60,60,67,0.08);
            padding: 0;
            margin: 0 0 8px;
            overflow-x: auto;
            scroll-behavior: smooth;
            position: relative;
            -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
            scrollbar-width: none; /* 隐藏滚动条 */
            -ms-overflow-style: none; /* 隐藏滚动条 */
        }
        
        .ios-tab-bar::-webkit-scrollbar {
            display: none; /* 隐藏滚动条 */
        }
        
        .ios-tab {
            flex: 1;
            min-width: 80px;
            text-align: center;
            font-size: 15px;
            font-weight: 500;
            color: var(--ios-secondaryLabel);
            padding: 12px 0;
            position: relative;
            transition: color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            white-space: nowrap;
        }
        
        .ios-tab.active {
            color: var(--ios-blue);
            font-weight: 600;
        }
        
        .ios-tab-indicator-container {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: transparent;
            overflow: hidden;
        }
        
        .ios-tab-indicator {
            position: absolute;
            bottom: 0;
            height: 2px;
            width: 24px; /* 固定宽度 */
            background-color: var(--ios-blue);
            border-radius: 1px;
            transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1); /* 更加精细的iOS动画曲线 */
            left: 50%;
            transform: translateX(-50%) scaleX(0);
            transform-origin: center;
            opacity: 0;
        }
        
        .ios-tab.active .ios-tab-indicator {
            opacity: 1;
            transform: translateX(-50%) scaleX(1);
        }
        
        /* 空白状态 */
        .ios-empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 300px;
            opacity: 0.8;
        }
        
        .ios-empty-icon {
            width: 64px;
            height: 64px;
            background-color: var(--ios-light-gray);
            border-radius: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            color: var(--ios-gray);
            font-size: 28px;
        }
        
        .ios-empty-text {
            font-size: 15px;
            color: var(--ios-secondaryLabel);
            text-align: center;
            max-width: 200px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen ios-scroll-indicator">
            <div class="screen-title">浏览历史</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="history.back()" class="ios-button ios-haptic text-[#007AFF]">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <h1 class="text-center font-semibold">浏览历史</h1>
                <div></div>
            </div>

            <!-- 分类标签 -->
            <div class="ios-tab-bar mb-2">
                <button class="ios-tab active ios-haptic" data-category="all">
                    全部
                    <div class="ios-tab-indicator"></div>
                </button>
                <button class="ios-tab ios-haptic" data-category="second-hand">
                    二手闲置
                    <div class="ios-tab-indicator"></div>
                </button>
                <button class="ios-tab ios-haptic" data-category="parking">
                    停车位
                    <div class="ios-tab-indicator"></div>
                </button>
                <button class="ios-tab ios-haptic" data-category="housing">
                    房源
                    <div class="ios-tab-indicator"></div>
                </button>
                <div class="ios-tab-indicator-container" id="tabIndicatorContainer"></div>
            </div>

            <!-- 历史记录列表 -->
            <div class="px-4 pb-32">
                <div id="history-content">
                    <!-- 今天 -->
                    <div class="ios-section-header">今天</div>
                    <div class="px-4">
                        <div class="ios-history-item ios-haptic ios-fade-in" data-category="second-hand">
                            <div class="ios-history-image">
                                <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff" class="w-full h-full object-cover">
                            </div>
                            <div class="ios-history-content">
                                <div class="ios-history-title">Nike Air Max 运动鞋</div>
                                <div class="ios-history-desc">9成新 | 尺码42 | ¥299</div>
                                <div class="ios-history-time">今天 14:30</div>
                            </div>
                            <i class="fas fa-chevron-right ios-chevron"></i>
                        </div>
                        
                        <div class="ios-history-item ios-haptic ios-fade-in" data-category="parking">
                            <div class="ios-history-image">
                                <img src="https://images.unsplash.com/photo-1573348722427-f1d6819fdf98" class="w-full h-full object-cover">
                            </div>
                            <div class="ios-history-content">
                                <div class="ios-history-title">阳光小区地下车位</div>
                                <div class="ios-history-desc">朝阳区 | 可短租 | ¥300/月</div>
                                <div class="ios-history-time">今天 11:25</div>
                            </div>
                            <i class="fas fa-chevron-right ios-chevron"></i>
                        </div>
                    </div>
                    
                    <!-- 昨天 -->
                    <div class="ios-section-header">昨天</div>
                    <div class="px-4">
                        <div class="ios-history-item ios-haptic ios-fade-in" data-category="housing">
                            <div class="ios-history-image">
                                <img src="https://images.unsplash.com/photo-1522708323590-d24dbb6b0267" class="w-full h-full object-cover">
                            </div>
                            <div class="ios-history-content">
                                <div class="ios-history-title">阳光花园 2室1厅</div>
                                <div class="ios-history-desc">80㎡ | 南北通透 | ¥3200/月</div>
                                <div class="ios-history-time">昨天 18:45</div>
                            </div>
                            <i class="fas fa-chevron-right ios-chevron"></i>
                        </div>
                        
                        <div class="ios-history-item ios-haptic ios-fade-in" data-category="second-hand">
                            <div class="ios-history-image">
                                <img src="https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa" class="w-full h-full object-cover">
                            </div>
                            <div class="ios-history-content">
                                <div class="ios-history-title">二手苹果手表 Apple Watch S6</div>
                                <div class="ios-history-desc">9.5成新 | 带原装充电器 | ¥1680</div>
                                <div class="ios-history-time">昨天 16:20</div>
                            </div>
                            <i class="fas fa-chevron-right ios-chevron"></i>
                        </div>
                    </div>
                    
                    <!-- 更早 -->
                    <div class="ios-section-header">更早</div>
                    <div class="px-4">
                        <div class="ios-history-item ios-haptic ios-fade-in" data-category="second-hand">
                            <div class="ios-history-image">
                                <img src="https://images.unsplash.com/photo-1523275335684-37898b6baf30" class="w-full h-full object-cover">
                            </div>
                            <div class="ios-history-content">
                                <div class="ios-history-title">智能手表充电底座</div>
                                <div class="ios-history-desc">全新 | 多功能充电器 | ¥89</div>
                                <div class="ios-history-time">3天前</div>
                            </div>
                            <i class="fas fa-chevron-right ios-chevron"></i>
                        </div>
                        
                        <div class="ios-history-item ios-haptic ios-fade-in" data-category="second-hand">
                            <div class="ios-history-image">
                                <img src="https://images.unsplash.com/photo-1543002588-bfa74002ed7e" class="w-full h-full object-cover">
                            </div>
                            <div class="ios-history-content">
                                <div class="ios-history-title">儿童二手自行车</div>
                                <div class="ios-history-desc">8成新 | 适合6-8岁 | ¥199</div>
                                <div class="ios-history-time">4天前</div>
                            </div>
                            <i class="fas fa-chevron-right ios-chevron"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 清除历史按钮 -->
                <button class="ios-clear-button ios-haptic">
                    清除全部浏览历史
                </button>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 增强标签切换功能
            const tabBar = document.querySelector('.ios-tab-bar');
            const tabs = document.querySelectorAll('.ios-tab');
            const historyItems = document.querySelectorAll('.ios-history-item');
            const indicatorContainer = document.getElementById('tabIndicatorContainer');
            
            // 创建活动指示器
            const activeIndicator = document.createElement('div');
            activeIndicator.className = 'ios-tab-indicator';
            indicatorContainer.appendChild(activeIndicator);
            
            // 更新指示器位置的函数
            function updateIndicator(tab) {
                const tabRect = tab.getBoundingClientRect();
                const tabBarRect = tabBar.getBoundingClientRect();
                const indicatorWidth = 24; // 与CSS中定义的宽度相同
                
                // 计算指示器位置，相对于tab-bar
                const indicatorLeft = tabRect.left - tabBarRect.left + (tabRect.width - indicatorWidth) / 2;
                
                // 设置指示器位置和透明度
                activeIndicator.style.width = `${indicatorWidth}px`;
                activeIndicator.style.left = `${indicatorLeft + indicatorWidth/2}px`;
                activeIndicator.style.transform = 'translateX(-50%) scaleX(1)';
                activeIndicator.style.opacity = '1';
                
                // 确保选中的标签在可视区域中
                const scrollLeft = tabBar.scrollLeft;
                const tabOffsetLeft = tabRect.left - tabBarRect.left + scrollLeft;
                const scrollCenter = tabOffsetLeft - (tabBarRect.width / 2) + (tabRect.width / 2);
                
                if (tabOffsetLeft < scrollLeft || tabOffsetLeft + tabRect.width > scrollLeft + tabBarRect.width) {
                    tabBar.scrollTo({
                        left: scrollCenter,
                        behavior: 'smooth'
                    });
                }
            }
            
            // 初始化指示器位置
            setTimeout(() => {
                const activeTab = document.querySelector('.ios-tab.active');
                if (activeTab) {
                    updateIndicator(activeTab);
                }
            }, 100);
            
            // 标签切换和内容筛选
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有标签的active类
                    tabs.forEach(t => {
                        t.classList.remove('active');
                        t.querySelector('.ios-tab-indicator').style.opacity = '0';
                    });
                    
                    // 给当前标签添加active类
                    this.classList.add('active');
                    
                    // 更新指示器位置
                    updateIndicator(this);
                    
                    const category = this.getAttribute('data-category');
                    
                    // 筛选历史项目
                    let visibleCount = 0;
                    historyItems.forEach(item => {
                        if (category === 'all' || item.getAttribute('data-category') === category) {
                            item.style.display = 'flex';
                            // 重置动画，并按顺序延迟显示
                            item.style.animation = 'none';
                            setTimeout(() => {
                                item.style.animation = '';
                                item.style.animationDelay = `${0.05 * visibleCount}s`;
                            }, 10);
                            visibleCount++;
                        } else {
                            item.style.display = 'none';
                        }
                    });
                    
                    // 处理标题的显示隐藏
                    const sectionHeaders = document.querySelectorAll('.ios-section-header');
                    sectionHeaders.forEach(header => {
                        const nextSection = header.nextElementSibling;
                        const visibleItems = nextSection.querySelectorAll('.ios-history-item[style*="display: flex"]');
                        
                        if (visibleItems.length > 0) {
                            header.style.display = 'block';
                            nextSection.style.display = 'block';
                        } else {
                            header.style.display = 'none';
                            nextSection.style.display = 'none';
                        }
                    });
                    
                    // 处理空状态
                    if (visibleCount === 0) {
                        // 检查是否已存在空状态提示
                        let emptyState = document.querySelector('.ios-empty-state');
                        if (!emptyState) {
                            emptyState = document.createElement('div');
                            emptyState.className = 'ios-empty-state';
                            emptyState.innerHTML = `
                                <div class="ios-empty-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="ios-empty-text">
                                    该分类下暂无浏览记录
                                </div>
                            `;
                            const historyContent = document.getElementById('history-content');
                            historyContent.appendChild(emptyState);
                        } else {
                            emptyState.style.display = 'flex';
                        }
                    } else {
                        // 隐藏空状态提示
                        const emptyState = document.querySelector('.ios-empty-state');
                        if (emptyState) {
                            emptyState.style.display = 'none';
                        }
                    }
                });
            });
            
            // 监听窗口大小变化，更新指示器位置
            window.addEventListener('resize', () => {
                const activeTab = document.querySelector('.ios-tab.active');
                if (activeTab) {
                    updateIndicator(activeTab);
                }
            });
            
            // 清除历史记录按钮
            const clearBtn = document.querySelector('.ios-clear-button');
            clearBtn.addEventListener('click', function() {
                // 显示iOS风格的确认对话框
                if (confirm('确定要清除全部浏览历史吗？此操作无法撤销。')) {
                    // 模拟清除历史记录的动画效果
                    const historyItems = document.querySelectorAll('.ios-history-item');
                    historyItems.forEach((item, index) => {
                        setTimeout(() => {
                            item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                            item.style.opacity = '0';
                            item.style.transform = 'translateX(50px)';
                        }, index * 50);
                    });
                    
                    // 隐藏标题
                    setTimeout(() => {
                        document.querySelectorAll('.ios-section-header').forEach(header => {
                            header.style.opacity = '0';
                            header.style.transition = 'opacity 0.3s ease';
                        });
                    }, historyItems.length * 50);
                    
                    // 动画完成后显示空状态
                    setTimeout(() => {
                        const historyContent = document.getElementById('history-content');
                        historyContent.innerHTML = `
                            <div class="ios-empty-state">
                                <div class="ios-empty-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="ios-empty-text">
                                    您的浏览历史已清空
                                </div>
                            </div>
                        `;
                    }, historyItems.length * 50 + 300);
                }
            });
        });
    </script>
</body>
</html> 