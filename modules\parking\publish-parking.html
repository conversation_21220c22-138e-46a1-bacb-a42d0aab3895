<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 发布停车位</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            background-color: rgba(255,255,255,0.97);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            padding-bottom: max(env(safe-area-inset-bottom), 16px);
            border-top: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 500;
            background-color: rgba(255,255,255,0.97);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS表单元素 */
        .ios-form-group {
            margin-bottom: 16px;
        }
        
        .ios-label {
            font-size: 15px;
            font-weight: 500;
            color: var(--ios-label);
            margin-bottom: 6px;
            display: block;
        }
        
        .ios-input {
            width: 100%;
            background-color: #fff;
            border: 0.5px solid rgba(0,0,0,0.1);
            border-radius: 10px;
            padding: 12px 16px;
            font-size: 15px;
            color: var(--ios-label);
            transition: all 0.2s ease;
        }
        
        .ios-input:focus {
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 1px rgba(0,122,255,0.2);
            outline: none;
        }
        
        .ios-input::placeholder {
            color: var(--ios-tertiaryLabel);
        }
        
        .ios-select {
            width: 100%;
            background-color: #fff;
            border: 0.5px solid rgba(0,0,0,0.1);
            border-radius: 10px;
            padding: 12px 16px;
            font-size: 15px;
            color: var(--ios-label);
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
        }
        
        .ios-select:focus {
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 1px rgba(0,122,255,0.2);
            outline: none;
        }
        
        /* iOS固定按钮 */
        .ios-fixed-btn {
            background-color: var(--ios-blue);
            color: white;
            border-radius: 12px;
            font-weight: 500;
            font-size: 16px;
            padding: 14px 0;
            width: 100%;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,122,255,0.3);
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), box-shadow 0.2s ease;
        }
        
        .ios-fixed-btn:active {
            transform: scale(0.98);
            box-shadow: 0 1px 4px rgba(0,122,255,0.2);
        }
        
        /* iOS分隔线 */
        .ios-separator {
            height: 0.5px;
            background-color: rgba(60,60,67,0.1);
            margin: 8px 0;
        }
        
        /* iOS表单卡片 */
        .ios-form-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 8px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.03);
        }
        
        /* iOS表单标题 */
        .ios-form-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--ios-label);
            margin-bottom: 12px;
        }
        
        /* iOS上传按钮 */
        .ios-upload-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(0,0,0,0.03);
            border-radius: 8px;
            border: 1px dashed rgba(0,0,0,0.1);
            padding: 24px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .ios-upload-btn:hover {
            background-color: rgba(0,0,0,0.05);
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(12px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.5s cubic-bezier(0.24, 0.22, 0.015, 1.0) forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        /* iOS单选和复选按钮 */
        .ios-radio-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 8px;
        }
        
        .ios-radio {
            display: none;
        }
        
        .ios-radio-label {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            background-color: white;
            border: 0.5px solid rgba(0,0,0,0.1);
            border-radius: 20px;
            font-size: 14px;
            color: var(--ios-secondaryLabel);
            transition: all 0.2s ease;
            user-select: none;
        }
        
        .ios-radio:checked + .ios-radio-label {
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
            border-color: var(--ios-blue);
        }
        
        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }
        
        /* iOS列表样式 */
        .ios-list-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 0.5px solid rgba(60,60,67,0.1);
        }
        
        .ios-list-item:last-child {
            border-bottom: none;
        }
        
        .ios-list-icon {
            width: 24px;
            color: var(--ios-gray);
            margin-right: 12px;
            text-align: center;
        }
        
        .ios-list-content {
            flex: 1;
        }
        
        .ios-list-label {
            font-size: 15px;
            color: var(--ios-label);
            margin-bottom: 2px;
        }
        
        .ios-list-value {
            font-size: 13px;
            color: var(--ios-secondaryLabel);
        }
        
        .ios-list-arrow {
            color: var(--ios-tertiaryLabel);
            margin-left: 8px;
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator">
            <div class="screen-title">发布停车位</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="window.location.href='parking-market.html'" class="ios-button ios-haptic">
                    <i class="fas fa-chevron-left text-[#007AFF] mr-1"></i>
                    <span class="text-[#007AFF]">返回</span>
                </button>
                <h1 class="text-center font-semibold">发布停车位</h1>
                <button onclick="window.location.href='../navigation/home.html'" class="ios-button ios-haptic">
                    <span class="text-[#007AFF]">取消</span>
                </button>
            </div>

            <div class="p-4 pb-32">
                <!-- 基础信息 -->
                <div class="ios-form-card ios-fade-in">
                    <h3 class="ios-form-title">基础信息</h3>
                    
                    <div class="ios-form-group">
                        <label class="ios-label">车位类型</label>
                        <div class="ios-radio-group">
                            <input type="radio" id="type-fixed" name="parking-type" class="ios-radio" checked>
                            <label for="type-fixed" class="ios-radio-label ios-haptic">固定车位</label>
                            
                            <input type="radio" id="type-floating" name="parking-type" class="ios-radio">
                            <label for="type-floating" class="ios-radio-label ios-haptic">非固定车位</label>
                            
                            <input type="radio" id="type-other" name="parking-type" class="ios-radio">
                            <label for="type-other" class="ios-radio-label ios-haptic">其他</label>
                        </div>
                    </div>
                    
                    <div class="ios-form-group">
                        <label class="ios-label">交易类型</label>
                        <div class="ios-radio-group">
                            <input type="radio" id="deal-rent" name="deal-type" class="ios-radio" checked>
                            <label for="deal-rent" class="ios-radio-label ios-haptic">出租</label>
                            
                            <input type="radio" id="deal-sell" name="deal-type" class="ios-radio">
                            <label for="deal-sell" class="ios-radio-label ios-haptic">出售</label>
                        </div>
                    </div>
                    
                    <div class="ios-form-group">
                        <label class="ios-label">价格设置</label>
                        <div class="relative">
                            <input type="number" placeholder="请输入价格" class="ios-input pl-10">
                            <span class="absolute left-4 top-[12px] text-[#8E8E93]">¥</span>
                        </div>
                        <div class="mt-2 text-xs text-[#8E8E93]">出租请填月租金，出售请填总价</div>
                    </div>
                </div>
                
                <!-- 车位信息 -->
                <div class="ios-form-card ios-fade-in" style="animation-delay: 0.1s;">
                    <h3 class="ios-form-title">车位信息</h3>
                    
                    <div class="ios-form-group">
                        <label class="ios-label">所在小区</label>
                        <input type="text" placeholder="请选择所在小区" class="ios-input" onclick="alert('选择小区')">
                    </div>
                    
                    <div class="ios-form-group">
                        <label class="ios-label">具体位置</label>
                        <select class="ios-select">
                            <option value="" disabled selected>请选择具体位置</option>
                            <option value="underground-1">地下负一层</option>
                            <option value="underground-2">地下负二层</option>
                            <option value="underground-3">地下负三层</option>
                            <option value="ground">地面停车场</option>
                            <option value="other">其他位置</option>
                        </select>
                    </div>
                    
                    <!-- 车位在地下室的位置 -->
                    <div class="ios-form-group mt-4">
                        <label class="ios-label">车位在地下室的位置</label>
                        <div class="bg-white border border-[rgba(0,0,0,0.1)] rounded-xl overflow-hidden">
                            <!-- 图例说明 -->
                            <div class="flex items-center justify-center space-x-4 py-2 bg-[#F2F2F7] px-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-gray-200 border border-gray-300 rounded-sm mr-1"></div>
                                    <span class="text-xs text-[#8E8E93]">空闲车位</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-[rgba(0,122,255,0.2)] border border-[#007AFF] rounded-sm mr-1"></div>
                                    <span class="text-xs text-[#8E8E93]">已选车位</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-[rgba(255,59,48,0.2)] border border-[#FF3B30] rounded-sm mr-1"></div>
                                    <span class="text-xs text-[#8E8E93]">已售车位</span>
                                </div>
                            </div>
                            
                            <!-- 车库地图容器 -->
                            <div class="relative overflow-x-auto ios-scroll-indicator p-4 bg-[rgba(0,122,255,0.05)] parking-map-container" style="min-height: 240px;" id="parkingMapContainer">
                                <!-- 入口/出口标记 -->
                                <div class="absolute top-0 left-1/2 transform -translate-x-1/2 bg-[#FF9500] text-white text-xs px-4 py-1 rounded-b-lg ios-button">
                                    <i class="fas fa-sign-in-alt mr-1"></i> 入口/出口
                                </div>
                                
                                <!-- 地下车库布局 -->
                                <div class="min-w-[680px] mt-8 pl-10" id="parkingMapContent">
                                    <!-- A区 -->
                                    <div class="flex justify-between mb-4 relative">
                                        <div class="text-xs font-medium text-[#8E8E93] absolute left-[-30px] top-1/2 transform -translate-y-1/2">A区</div>
                                        <div class="flex space-x-1 ml-1">
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">A01</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">A02</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">A03</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">A04</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">A05</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">A06</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">A07</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">A08</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">A09</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">A10</button>
                                            <button class="w-8 h-12 bg-[rgba(255,59,48,0.2)] border border-[#FF3B30] rounded-sm flex items-center justify-center text-xs text-[#FF3B30] ios-haptic ios-button transition-all">A11</button>
                                            <button class="w-8 h-12 bg-[rgba(255,59,48,0.2)] border border-[#FF3B30] rounded-sm flex items-center justify-center text-xs text-[#FF3B30] ios-haptic ios-button transition-all">A12</button>
                                        </div>
                                    </div>
                                    
                                    <!-- 车道 -->
                                    <div class="h-8 bg-gray-300 rounded-md flex items-center justify-center text-xs text-gray-600 mb-4">
                                        <i class="fas fa-arrow-right mr-2"></i> 车道 <i class="fas fa-arrow-left ml-2"></i>
                                    </div>
                                    
                                    <!-- B区 -->
                                    <div class="flex justify-between mb-4 relative">
                                        <div class="text-xs font-medium text-[#8E8E93] absolute left-[-30px] top-1/2 transform -translate-y-1/2">B区</div>
                                        <div class="flex space-x-1 ml-1">
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">B01</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">B02</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">B03</button>
                                            <button class="w-8 h-12 bg-[rgba(0,122,255,0.2)] border border-[#007AFF] rounded-sm flex items-center justify-center text-xs text-[#007AFF] ios-haptic ios-button transition-all selected-space" onclick="selectParkingSpace(this)">B04</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">B05</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">B06</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">B07</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">B08</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">B09</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">B10</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">B11</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">B12</button>
                                        </div>
                                    </div>
                                    
                                    <!-- 车道 -->
                                    <div class="h-8 bg-gray-300 rounded-md flex items-center justify-center text-xs text-gray-600 mb-4">
                                        <i class="fas fa-arrow-right mr-2"></i> 车道 <i class="fas fa-arrow-left ml-2"></i>
                                    </div>
                                    
                                    <!-- C区 -->
                                    <div class="flex justify-between relative">
                                        <div class="text-xs font-medium text-[#8E8E93] absolute left-[-30px] top-1/2 transform -translate-y-1/2">C区</div>
                                        <div class="flex space-x-1 ml-1">
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">C01</button>
                                            <button class="w-8 h-12 bg-[rgba(255,59,48,0.2)] border border-[#FF3B30] rounded-sm flex items-center justify-center text-xs text-[#FF3B30] ios-haptic ios-button transition-all">C02</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">C03</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">C04</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">C05</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">C06</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">C07</button>
                                            <button class="w-8 h-12 bg-[rgba(255,59,48,0.2)] border border-[#FF3B30] rounded-sm flex items-center justify-center text-xs text-[#FF3B30] ios-haptic ios-button transition-all">C08</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">C09</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">C10</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">C11</button>
                                            <button class="w-8 h-12 bg-gray-200 border border-gray-300 rounded-sm flex items-center justify-center text-xs text-gray-500 ios-haptic ios-button transition-all" onclick="selectParkingSpace(this)">C12</button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 滑动提示 -->
                                <div class="absolute bottom-2 right-2 bg-white bg-opacity-90 rounded-full px-2 py-1 text-xs text-[#8E8E93] shadow-sm flex items-center ios-button">
                                    <i class="fas fa-arrows-alt-h mr-1 text-[10px]"></i> 
                                    <span id="dragHint">拖动或左右滑动查看更多</span>
                                </div>
                            </div>
                            
                            <div class="px-4 py-3 border-t border-[rgba(0,0,0,0.05)]">
                                <div class="flex gap-4">
                                    <div class="flex-1">
                                        <label class="block text-xs text-[#8E8E93] mb-1">选择区域</label>
                                        <select class="ios-select text-sm py-2" id="parkingArea">
                                            <option value="B">B区</option>
                                            <option value="A">A区</option>
                                            <option value="C">C区</option>
                                        </select>
                                    </div>
                                    <div class="flex-1">
                                        <label class="block text-xs text-[#8E8E93] mb-1">车位编号</label>
                                        <input type="text" value="B04" class="ios-input text-sm py-2" id="parkingNumber">
                                    </div>
                                </div>
                                <p class="text-xs text-[#8E8E93] mt-2">标记车位位置可以帮助用户更直观地了解车位情况</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="ios-form-group">
                        <label class="ios-label">车位尺寸</label>
                        <div class="flex gap-2">
                            <div class="relative flex-1">
                                <input type="number" placeholder="长" class="ios-input pl-8 pr-8 text-center">
                                <span class="absolute right-3 top-[12px] text-xs text-[#8E8E93]">米</span>
                            </div>
                            <div class="text-[#8E8E93] flex items-center">×</div>
                            <div class="relative flex-1">
                                <input type="number" placeholder="宽" class="ios-input pl-8 pr-8 text-center">
                                <span class="absolute right-3 top-[12px] text-xs text-[#8E8E93]">米</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="ios-form-group">
                        <label class="ios-label">车位编号</label>
                        <input type="text" placeholder="请输入车位编号（选填）" class="ios-input">
                    </div>
                </div>
                
                <!-- 上传照片 -->
                <div class="ios-form-card ios-fade-in" style="animation-delay: 0.15s;">
                    <h3 class="ios-form-title">上传照片</h3>
                    
                    <div class="mb-3 text-xs text-[#8E8E93]">请上传车位的清晰照片，最多6张，第一张将作为封面</div>
                    
                    <div class="grid grid-cols-3 gap-2">
                        <div class="ios-upload-btn ios-haptic">
                            <i class="fas fa-plus text-[#8E8E93]"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 特色标签 -->
                <div class="ios-form-card ios-fade-in" style="animation-delay: 0.2s;">
                    <h3 class="ios-form-title">特色标签</h3>
                    
                    <div class="flex flex-wrap gap-2">
                        <label class="ios-haptic cursor-pointer">
                            <input type="checkbox" class="hidden peer">
                            <span class="inline-block px-3 py-2 text-sm rounded-lg bg-white border border-[#E5E5EA] peer-checked:bg-[rgba(0,122,255,0.1)] peer-checked:border-[#007AFF] peer-checked:text-[#007AFF] transition-colors">24小时保安</span>
                        </label>
                        <label class="ios-haptic cursor-pointer">
                            <input type="checkbox" class="hidden peer">
                            <span class="inline-block px-3 py-2 text-sm rounded-lg bg-white border border-[#E5E5EA] peer-checked:bg-[rgba(0,122,255,0.1)] peer-checked:border-[#007AFF] peer-checked:text-[#007AFF] transition-colors">有充电桩</span>
                        </label>
                        <label class="ios-haptic cursor-pointer">
                            <input type="checkbox" class="hidden peer">
                            <span class="inline-block px-3 py-2 text-sm rounded-lg bg-white border border-[#E5E5EA] peer-checked:bg-[rgba(0,122,255,0.1)] peer-checked:border-[#007AFF] peer-checked:text-[#007AFF] transition-colors">临近电梯</span>
                        </label>
                        <label class="ios-haptic cursor-pointer">
                            <input type="checkbox" class="hidden peer">
                            <span class="inline-block px-3 py-2 text-sm rounded-lg bg-white border border-[#E5E5EA] peer-checked:bg-[rgba(0,122,255,0.1)] peer-checked:border-[#007AFF] peer-checked:text-[#007AFF] transition-colors">可短租</span>
                        </label>
                        <label class="ios-haptic cursor-pointer">
                            <input type="checkbox" class="hidden peer">
                            <span class="inline-block px-3 py-2 text-sm rounded-lg bg-white border border-[#E5E5EA] peer-checked:bg-[rgba(0,122,255,0.1)] peer-checked:border-[#007AFF] peer-checked:text-[#007AFF] transition-colors">带储物间</span>
                        </label>
                        <label class="ios-haptic cursor-pointer">
                            <input type="checkbox" class="hidden peer">
                            <span class="inline-block px-3 py-2 text-sm rounded-lg bg-white border border-[#E5E5EA] peer-checked:bg-[rgba(0,122,255,0.1)] peer-checked:border-[#007AFF] peer-checked:text-[#007AFF] transition-colors">临近地铁</span>
                        </label>
                    </div>
                </div>
                
                <!-- 补充描述 -->
                <div class="ios-form-card ios-fade-in" style="animation-delay: 0.25s;">
                    <h3 class="ios-form-title">补充描述</h3>
                    
                    <textarea placeholder="请输入车位的补充信息，如使用须知、周边环境等" class="ios-input min-h-[100px] resize-none"></textarea>
                </div>
                
                <!-- 联系方式 -->
                <div class="ios-form-card ios-fade-in" style="animation-delay: 0.3s;">
                    <h3 class="ios-form-title">联系方式</h3>
                    
                    <div class="ios-form-group">
                        <label class="ios-label">联系人</label>
                        <input type="text" placeholder="请输入联系人姓名" class="ios-input">
                    </div>
                    
                    <div class="ios-form-group">
                        <label class="ios-label">联系电话</label>
                        <input type="tel" placeholder="请输入联系电话" class="ios-input">
                    </div>
                    
                    <div class="ios-form-group mb-0">
                        <label class="ios-label">微信号</label>
                        <input type="text" placeholder="请输入微信号（选填）" class="ios-input">
                    </div>
                </div>
            </div>
            
            <!-- 底部发布按钮 -->
            <div class="fixed bottom-0 left-0 right-0 ios-bottom-nav p-4 z-40">
                <button class="ios-fixed-btn ios-button ios-haptic">立即发布</button>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载后执行的脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 初始化滚动到已选中的车位
            scrollToSelectedSpace();
            
            // 监听区域选择框变化
            document.getElementById('parkingArea').addEventListener('change', function() {
                updateParkingNumber();
            });
            
            // 监听车位编号输入框变化
            document.getElementById('parkingNumber').addEventListener('input', function() {
                updateSelectedSpace();
            });
            
            // 初始化拖拽功能
            initDraggableMap();
        });
        
        // 初始化拖拽功能
        function initDraggableMap() {
            const container = document.getElementById('parkingMapContainer');
            const content = document.getElementById('parkingMapContent');
            const dragHint = document.getElementById('dragHint');
            
            let isDragging = false;
            let startX, startY, scrollLeft, scrollTop;
            
            // 鼠标事件
            container.addEventListener('mousedown', function(e) {
                isDragging = true;
                container.style.cursor = 'grabbing';
                startX = e.pageX - container.offsetLeft;
                startY = e.pageY - container.offsetTop;
                scrollLeft = container.scrollLeft;
                scrollTop = container.scrollTop;
                
                // 改变提示文字
                dragHint.textContent = '正在拖动';
            });
            
            container.addEventListener('mouseleave', function() {
                isDragging = false;
                container.style.cursor = 'default';
                dragHint.textContent = '拖动或左右滑动查看更多';
            });
            
            container.addEventListener('mouseup', function() {
                isDragging = false;
                container.style.cursor = 'default';
                dragHint.textContent = '拖动或左右滑动查看更多';
            });
            
            container.addEventListener('mousemove', function(e) {
                if (!isDragging) return;
                e.preventDefault();
                
                const x = e.pageX - container.offsetLeft;
                const walkX = (x - startX) * 1.5; // 乘以系数使拖动更灵敏
                container.scrollLeft = scrollLeft - walkX;
                
                // 防止溢出边界
                if (container.scrollLeft < 0) {
                    container.scrollLeft = 0;
                } else if (container.scrollLeft > content.offsetWidth - container.offsetWidth) {
                    container.scrollLeft = content.offsetWidth - container.offsetWidth;
                }
            });
            
            // 触摸事件支持
            container.addEventListener('touchstart', function(e) {
                const touch = e.touches[0];
                startX = touch.pageX - container.offsetLeft;
                startY = touch.pageY - container.offsetTop;
                scrollLeft = container.scrollLeft;
                scrollTop = container.scrollTop;
                
                // 改变提示文字
                dragHint.textContent = '正在滑动';
            });
            
            container.addEventListener('touchend', function() {
                dragHint.textContent = '拖动或左右滑动查看更多';
            });
            
            container.addEventListener('touchmove', function(e) {
                e.preventDefault(); // 防止页面滚动
                const touch = e.touches[0];
                const x = touch.pageX - container.offsetLeft;
                const walkX = (x - startX) * 1.5;
                container.scrollLeft = scrollLeft - walkX;
            });
            
            // 添加样式以指示可拖动
            container.style.cursor = 'grab';
            
            // 防止选中文本干扰拖动体验
            container.addEventListener('selectstart', function(e) {
                if (isDragging) {
                    e.preventDefault();
                }
            });
        }
        
        // 选择车位
        function selectParkingSpace(element) {
            // 移除之前选中的车位
            const selectedSpace = document.querySelector('.selected-space');
            if (selectedSpace) {
                selectedSpace.classList.remove('selected-space');
                selectedSpace.classList.remove('bg-[rgba(0,122,255,0.2)]', 'border-[#007AFF]', 'text-[#007AFF]');
                selectedSpace.classList.add('bg-gray-200', 'border-gray-300', 'text-gray-500');
            }
            
            // 标记当前选中的车位
            element.classList.remove('bg-gray-200', 'border-gray-300', 'text-gray-500');
            element.classList.add('bg-[rgba(0,122,255,0.2)]', 'border-[#007AFF]', 'text-[#007AFF]', 'selected-space');
            
            // 更新输入框
            const parkingNumber = element.textContent;
            document.getElementById('parkingNumber').value = parkingNumber;
            document.getElementById('parkingArea').value = parkingNumber.charAt(0);
            
            // 提供触感反馈
            if ('vibrate' in navigator) {
                navigator.vibrate(15);
            }
        }
        
        // 更新选中的车位
        function updateSelectedSpace() {
            const parkingArea = document.getElementById('parkingArea').value;
            const parkingNumber = document.getElementById('parkingNumber').value;
            
            // 查找车位元素
            const spaces = document.querySelectorAll('.ios-haptic:not(.bg-[rgba(255,59,48,0.2)])'); // 排除已售车位
            
            // 移除之前选中的车位
            const selectedSpace = document.querySelector('.selected-space');
            if (selectedSpace) {
                selectedSpace.classList.remove('selected-space');
                selectedSpace.classList.remove('bg-[rgba(0,122,255,0.2)]', 'border-[#007AFF]', 'text-[#007AFF]');
                selectedSpace.classList.add('bg-gray-200', 'border-gray-300', 'text-gray-500');
            }
            
            // 查找匹配的车位
            for (const space of spaces) {
                if (space.textContent === parkingNumber) {
                    space.classList.remove('bg-gray-200', 'border-gray-300', 'text-gray-500');
                    space.classList.add('bg-[rgba(0,122,255,0.2)]', 'border-[#007AFF]', 'text-[#007AFF]', 'selected-space');
                    
                    // 滚动到可见
                    scrollToSelectedSpace();
                    break;
                }
            }
        }
        
        // 根据区域更新车位编号
        function updateParkingNumber() {
            const parkingArea = document.getElementById('parkingArea').value;
            const currentNumber = document.getElementById('parkingNumber').value;
            
            // 如果区域不变，不更新
            if (currentNumber.charAt(0) === parkingArea) {
                return;
            }
            
            // 提取数字部分
            const numberPart = currentNumber.substring(1);
            
            // 设置新的车位编号
            document.getElementById('parkingNumber').value = parkingArea + numberPart;
            
            // 更新选中的车位
            updateSelectedSpace();
        }
        
        // 滚动到选中的车位
        function scrollToSelectedSpace() {
            const selectedSpace = document.querySelector('.selected-space');
            if (selectedSpace) {
                // 获取容器
                const container = selectedSpace.closest('.overflow-x-auto');
                if (container) {
                    // 计算位置并滚动
                    const containerRect = container.getBoundingClientRect();
                    const selectedRect = selectedSpace.getBoundingClientRect();
                    
                    // 计算水平中心点
                    const targetScrollLeft = selectedSpace.offsetLeft - (containerRect.width / 2) + (selectedRect.width / 2);
                    
                    // 使用平滑滚动
                    container.scrollTo({
                        left: targetScrollLeft,
                        behavior: 'smooth'
                    });
                }
            }
        }
    </script>
</body>
</html> 