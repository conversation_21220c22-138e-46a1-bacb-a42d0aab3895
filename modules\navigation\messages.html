<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 消息</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
        }
        
        /* 深色模式支持 - 已注释掉，避免系统默认使用深色模式 */
        @media (prefers-color-scheme: dark) {
            body.dark-theme {
                --ios-systemBackground: #000000;
                --ios-secondarySystemBackground: #1C1C1E;
                --ios-tertiarySystemBackground: #2C2C2E;
                --ios-groupedBackground: #000000;
                --ios-card: #1C1C1E;
                
                --ios-label: #FFFFFF;
                --ios-secondaryLabel: #EBEBF599;
                --ios-tertiaryLabel: #EBEBF54D;
                --ios-quaternaryLabel: #EBEBF52E;
            }
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow */
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .ios-card:active {
            transform: scale(0.98);
            box-shadow: 0 1px 3px rgba(0,0,0,0.02);
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding-bottom: env(safe-area-inset-bottom);
            border-top: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: 0.7;
        }
        
        /* iOS搜索框样式 */
        .ios-search {
            border-radius: 10px;
            background-color: var(--ios-light-gray);
            height: 36px;
            transition: background-color 0.2s ease;
        }
        
        .ios-search:focus-within {
            background-color: #E0E0E6;
        }
        
        /* iOS标题文字样式 */
        .ios-section-title {
            font-weight: 600;
            letter-spacing: -0.01em;
            color: var(--ios-label);
            font-size: 20px;
        }
        
        /* iOS标签样式 */
        .ios-tag {
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
            padding: 2px 8px;
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
        }
        
        /* iOS隔离视图风格 */
        .ios-grouped-section {
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
        }
        
        /* iOS列表分割线样式 */
        .ios-separator {
            height: 0.5px;
            background-color: rgba(60,60,67,0.1);
            margin-left: 16px;
        }

        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 600;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS动画效果 */
        @keyframes ios-ripple {
            0% {
                transform: scale(0.8);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }
        
        /* SF符号效果 */
        .sf-symbol {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            font-size: 14px;
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        .prototype-screen {
            background-color: var(--ios-secondarySystemBackground);
        }

        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }
        
        /* 平滑滚动 */
        .smooth-scroll {
            scroll-behavior: smooth;
        }
        
        /* 水波纹效果 */
        .ios-ripple-effect {
            position: relative;
            overflow: hidden;
        }

        .ios-ripple-effect::after {
            content: "";
            display: block;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            background-image: radial-gradient(circle, rgba(0, 0, 0, 0.1) 10%, transparent 10.01%);
            background-repeat: no-repeat;
            background-position: 50%;
            transform: scale(10, 10);
            opacity: 0;
            transition: transform 0.4s, opacity 0.8s;
        }

        .ios-ripple-effect:active::after {
            transform: scale(0, 0);
            opacity: 0.3;
            transition: 0s;
        }
        
        /* 内容区域 */
        .ios-content-area {
            padding-top: 4px;
            padding-bottom: 100px;
        }
        
        /* 消息列表样式 */
        .ios-message-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background-color: white;
            transition: background-color 0.2s ease;
        }
        
        .ios-message-item:active {
            background-color: #F2F2F7;
        }
        
        /* 消息图标样式 */
        .ios-icon-wrapper {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 未读消息红点 */
        .ios-badge {
            position: absolute;
            top: -4px;
            right: -4px;
            background-color: var(--ios-red);
            color: white;
            font-size: 11px;
            font-weight: 600;
            min-width: 18px;
            height: 18px;
            border-radius: 9px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 5px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        /* 功能模块样式 */
        .ios-function-module {
            padding: 6px 0;
        }
        
        /* 功能图标样式 */
        .ios-function-icon {
            width: 52px;
            height: 52px;
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 6px;
            transition: transform 0.2s ease;
        }
        
        .ios-function-button:active .ios-function-icon {
            transform: scale(0.92);
        }
        
        /* 通知项样式 */
        .ios-notification-item {
            border-radius: 12px;
            overflow: hidden;
            transition: transform 0.2s ease, background-color 0.2s ease;
        }
        
        .ios-notification-item:active {
            transform: scale(0.98);
            background-color: rgba(0,0,0,0.02);
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator">
            <div class="screen-title">消息</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>

            <div class="ios-content-area">
                <!-- 消息分类 -->
                <div class="ios-card mx-4 my-4 py-3 ios-fade-in">
                    <div class="ios-function-module grid grid-cols-4 gap-2">
                        <button class="ios-function-button flex flex-col items-center ios-button ios-haptic">
                            <div class="ios-function-icon relative" style="background: linear-gradient(145deg, rgba(0,122,255,0.12), rgba(90,200,250,0.12))">
                                <i class="fas fa-bell text-[#007AFF] text-lg"></i>
                                <span class="ios-badge">2</span>
                            </div>
                            <span class="text-[11px] font-medium text-center">系统通知</span>
                        </button>
                        <button class="ios-function-button flex flex-col items-center ios-button ios-haptic">
                            <div class="ios-function-icon" style="background: linear-gradient(145deg, rgba(52,199,89,0.12), rgba(48,209,88,0.12))">
                                <i class="fas fa-comment-dots text-[#34C759] text-lg"></i>
                            </div>
                            <span class="text-[11px] font-medium text-center">评论回复</span>
                        </button>
                        <button class="ios-function-button flex flex-col items-center ios-button ios-haptic">
                            <div class="ios-function-icon" style="background: linear-gradient(145deg, rgba(88,86,214,0.12), rgba(94,92,230,0.12))">
                                <i class="fas fa-heart text-[#5856D6] text-lg"></i>
                            </div>
                            <span class="text-[11px] font-medium text-center">点赞收藏</span>
                        </button>
                        <button class="ios-function-button flex flex-col items-center ios-button ios-haptic">
                            <div class="ios-function-icon" style="background: linear-gradient(145deg, rgba(255,149,0,0.12), rgba(255,159,10,0.12))">
                                <i class="fas fa-user-plus text-[#FF9500] text-lg"></i>
                            </div>
                            <span class="text-[11px] font-medium text-center">新增关注</span>
                        </button>
                    </div>
                </div>

                <!-- 聊天列表标题 -->
                <div class="px-4 mb-2">
                    <h2 class="text-[15px] font-semibold text-[#8E8E93]">聊天</h2>
                </div>
                
                <!-- 聊天列表 -->
                <div class="ios-card mx-4 mb-6 ios-fade-in overflow-hidden" style="animation-delay: 0.1s;">
                    <!-- 聊天项 1 -->
                    <div class="ios-message-item ios-button ios-haptic">
                        <div class="relative mr-3.5">
                            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde" class="w-12 h-12 rounded-full object-cover border border-gray-100">
                            <span class="ios-badge">3</span>
                        </div>
                        <div class="flex-1 py-0.5">
                            <div class="flex justify-between items-center mb-1">
                                <h3 class="text-[15px] font-semibold">张三</h3>
                                <span class="text-xs text-[#8E8E93]">10:30</span>
                            </div>
                            <p class="text-[13px] text-[#8E8E93] truncate pr-4">请问iPhone 14还在吗？可以便宜点吗</p>
                        </div>
                    </div>
                    
                    <div class="ios-separator"></div>
                    
                    <!-- 聊天项 2 -->
                    <div class="ios-message-item ios-button ios-haptic">
                        <div class="relative mr-3.5">
                            <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36" class="w-12 h-12 rounded-full object-cover border border-gray-100">
                            <span class="ios-badge">1</span>
                        </div>
                        <div class="flex-1 py-0.5">
                            <div class="flex justify-between items-center mb-1">
                                <h3 class="text-[15px] font-semibold">李四</h3>
                                <span class="text-xs text-[#8E8E93]">昨天</span>
                            </div>
                            <p class="text-[13px] text-[#8E8E93] truncate pr-4">阳光花园的车位什么时候可以看？</p>
                        </div>
                    </div>
                    
                    <div class="ios-separator"></div>
                    
                    <!-- 聊天项 3 -->
                    <div class="ios-message-item ios-button ios-haptic">
                        <div class="mr-3.5">
                            <img src="https://images.unsplash.com/photo-1527980965255-d3b416303d12" class="w-12 h-12 rounded-full object-cover border border-gray-100">
                        </div>
                        <div class="flex-1 py-0.5">
                            <div class="flex justify-between items-center mb-1">
                                <h3 class="text-[15px] font-semibold">王五</h3>
                                <span class="text-xs text-[#8E8E93]">周一</span>
                            </div>
                            <p class="text-[13px] text-[#8E8E93] truncate pr-4">好的，那就这么说定了</p>
                        </div>
                    </div>
                    
                    <div class="ios-separator"></div>
                    
                    <!-- 聊天项 4 -->
                    <div class="ios-message-item ios-button ios-haptic">
                        <div class="mr-3.5">
                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80" class="w-12 h-12 rounded-full object-cover border border-gray-100">
                        </div>
                        <div class="flex-1 py-0.5">
                            <div class="flex justify-between items-center mb-1">
                                <h3 class="text-[15px] font-semibold">赵六</h3>
                                <span class="text-xs text-[#8E8E93]">上周</span>
                            </div>
                            <p class="text-[13px] text-[#8E8E93] truncate pr-4">房子已经租出去了，谢谢关注</p>
                        </div>
                    </div>
                </div>
                
                <!-- 系统消息区域 -->
                <div class="px-4 mb-6 ios-fade-in" style="animation-delay: 0.15s;">
                    <div class="flex justify-between items-center mb-3">
                        <h2 class="ios-section-title">系统通知</h2>
                        <a href="#" class="text-sm text-[#007AFF] flex items-center ios-button px-2">
                            查看全部
                            <i class="fas fa-chevron-right ml-1 text-[10px]"></i>
                        </a>
                    </div>
                    <div class="ios-notification-item ios-card p-4 ios-button ios-haptic">
                        <div class="flex space-x-3.5">
                            <div class="w-11 h-11 rounded-full bg-[rgba(0,122,255,0.1)] flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-bell text-[#007AFF] text-base"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-[15px] font-semibold mb-1">物业通知</h3>
                                <p class="text-[14px] text-[#8E8E93] leading-snug mb-1.5">今日小区将进行消防检查，请业主配合</p>
                                <p class="text-xs text-[#8E8E93]">10分钟前</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 提示信息区域 -->
                <div class="px-4 mb-10 ios-fade-in" style="animation-delay: 0.2s;">
                    <div class="ios-card p-4 bg-[rgba(52,199,89,0.1)] border border-[rgba(52,199,89,0.2)]">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 rounded-full bg-[#34C759] flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-shield-alt text-white text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-[15px] font-medium text-[#34C759]">消息安全提示</h3>
                                <p class="text-[13px] text-[#3C3C43B2] mt-0.5">请注意保护个人信息安全，不要轻易透露个人信息</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="fixed bottom-0 left-0 right-0 ios-bottom-nav flex justify-around pt-1.5 pb-6 z-40">
                <button onclick="window.location.href='home.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-home text-[#8E8E93] text-[22px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">首页</span>
                </button>
                <button onclick="window.location.href='discover.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-compass text-[#8E8E93] text-[22px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">发现</span>
                </button>
                <button class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <div class="w-[50px] h-[50px] bg-[#007AFF] rounded-full flex items-center justify-center -mt-5 shadow-lg" style="box-shadow: 0 3px 10px rgba(0,122,255,0.3);">
                        <i class="fas fa-plus text-white text-xl"></i>
                    </div>
                </button>
                <button class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-comment text-[#007AFF] text-[22px]"></i>
                    <span class="text-[10px] text-[#007AFF] mt-0.5 font-medium">消息</span>
                </button>
                <button onclick="window.location.href='../profile/profile.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-user text-[#8E8E93] text-[22px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">我的</span>
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载后执行的脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
        });
    </script>
</body>
</html> 