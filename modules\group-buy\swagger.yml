openapi: 3.0.3
info:
  title: 乐享友邻 - 邻里拼单API
  description: 邻里拼单平台后端接口文档，包含拼单活动管理、参与拼单、拼单搜索等功能
  version: 1.0.0

servers:
  - url: https://dev-api.hoodly-joy.com/v1
    description: 开发环境

security:
  - BearerAuth: []

paths:
  /group-buy/activities:
    post:
      tags:
        - 拼单管理
      summary: 获取拼单活动列表
      description: 根据分类、状态等条件获取拼单活动列表，支持分页和筛选
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupBuyListRequest'
      responses:
        '200':
          description: 成功获取拼单活动列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupBuyListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /group-buy/activities/{activityId}:
    get:
      tags:
        - 拼单管理
      summary: 获取拼单活动详情
      description: 根据活动ID获取拼单活动的详细信息
      parameters:
        - name: activityId
          in: path
          required: true
          description: 拼单活动唯一标识ID
          schema:
            type: string
            example: "activity_123456"
      responses:
        '200':
          description: 成功获取拼单活动详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupBuyDetailResponse'
        '404':
          description: 拼单活动不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /group-buy/activities/publish:
    post:
      tags:
        - 拼单管理
      summary: 发布拼单活动
      description: 用户发布新的拼单活动
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishGroupBuyRequest'
      responses:
        '201':
          description: 拼单活动发布成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublishGroupBuyResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /group-buy/activities/search:
    post:
      tags:
        - 拼单搜索
      summary: 搜索拼单活动
      description: 根据关键词搜索拼单活动
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupBuySearchRequest'
      responses:
        '200':
          description: 搜索成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupBuyListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /group-buy/activities/{activityId}/join:
    post:
      tags:
        - 拼单参与
      summary: 参与拼单活动
      description: 用户参与指定的拼单活动
      parameters:
        - name: activityId
          in: path
          required: true
          description: 拼单活动唯一标识ID
          schema:
            type: string
            example: "activity_123456"
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JoinGroupBuyRequest'
      responses:
        '200':
          description: 参与拼单成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JoinGroupBuyResponse'
        '400':
          description: 请求参数错误或拼单已满
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /group-buy/activities/{activityId}/leave:
    post:
      tags:
        - 拼单参与
      summary: 退出拼单活动
      description: 用户退出已参与的拼单活动
      parameters:
        - name: activityId
          in: path
          required: true
          description: 拼单活动唯一标识ID
          schema:
            type: string
            example: "activity_123456"
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: 退出原因
                  example: "临时有事无法参与"
      responses:
        '200':
          description: 退出拼单成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 请求参数错误或无法退出
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /group-buy/activities/{activityId}/participants:
    get:
      tags:
        - 拼单参与
      summary: 获取拼单参与者列表
      description: 获取指定拼单活动的参与者列表
      parameters:
        - name: activityId
          in: path
          required: true
          description: 拼单活动唯一标识ID
          schema:
            type: string
            example: "activity_123456"
      responses:
        '200':
          description: 成功获取参与者列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParticipantsResponse'
        '404':
          description: 拼单活动不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /group-buy/categories:
    get:
      tags:
        - 拼单分类
      summary: 获取拼单分类列表
      description: 获取所有拼单活动分类信息
      responses:
        '200':
          description: 成功获取分类列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoriesResponse'

  /group-buy/my-activities:
    get:
      tags:
        - 用户拼单
      summary: 获取我的拼单活动
      description: 获取当前用户发布的和参与的拼单活动
      parameters:
        - name: type
          in: query
          description: 活动类型筛选
          schema:
            type: string
            enum: [all, published, joined]
            default: all
        - name: status
          in: query
          description: 活动状态筛选
          schema:
            type: string
            enum: [all, active, completed, expired, cancelled]
            default: all
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: pageSize
          in: query
          description: 每页数量
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 20
      responses:
        '200':
          description: 成功获取我的拼单活动
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MyActivitiesResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /upload/group-buy-images:
    post:
      tags:
        - 文件上传
      summary: 上传拼单活动图片
      description: 上传拼单活动相关图片，支持多张图片上传
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: 图片文件数组，最多9张
      responses:
        '200':
          description: 上传成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadResponse'
        '400':
          description: 上传失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    GroupBuyListRequest:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
        filters:
          type: object
          properties:
            category:
              type: string
              description: 拼单分类
              example: "超市果蔬"
              enum: ["全部", "超市果蔬", "生鲜海鲜", "电商商品", "餐饮外卖", "其他"]
            status:
              type: string
              description: 拼单状态
              example: "active"
              enum: ["all", "active", "completed", "expired", "cancelled"]
            location:
              type: object
              properties:
                communityId:
                  type: string
                  description: 小区ID
                  example: "community_123"
                radius:
                  type: number
                  description: 搜索半径（公里）
                  example: 5
                  default: 5
            timeRange:
              type: object
              properties:
                startTime:
                  type: string
                  format: date-time
                  description: 开始时间
                  example: "2024-01-15T00:00:00Z"
                endTime:
                  type: string
                  format: date-time
                  description: 结束时间
                  example: "2024-01-16T23:59:59Z"
        sortBy:
          type: string
          enum: [time_desc, time_asc, participants_desc, deadline_asc]
          default: time_desc
          description: 排序方式
      example:
        page: 1
        pageSize: 20
        filters:
          category: "超市果蔬"
          status: "active"
          location:
            communityId: "community_123"
            radius: 5
        sortBy: "time_desc"

    GroupBuyListResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            total:
              type: integer
              description: 总记录数
              example: 45
            page:
              type: integer
              description: 当前页码
              example: 1
            pageSize:
              type: integer
              description: 每页数量
              example: 20
            totalPages:
              type: integer
              description: 总页数
              example: 3
            activities:
              type: array
              items:
                $ref: '#/components/schemas/GroupBuyItem'

    GroupBuyItem:
      type: object
      properties:
        id:
          type: string
          description: 拼单活动ID
          example: "activity_123456"
        title:
          type: string
          description: 拼单标题
          example: "盒马鲜生水果拼单"
        description:
          type: string
          description: 拼单描述
          example: "本周末去盒马采购水果，有需要带的吗？车厘子、葡萄、苹果、橙子都可以，满300免运费~"
        category:
          type: string
          description: 拼单分类
          example: "超市果蔬"
        images:
          type: array
          items:
            type: string
          description: 拼单图片列表
          example: ["https://images.example.com/fruit1.jpg", "https://images.example.com/fruit2.jpg"]
        organizer:
          type: object
          properties:
            id:
              type: string
              description: 发起人ID
              example: "user_789"
            name:
              type: string
              description: 发起人姓名
              example: "王小花"
            avatar:
              type: string
              description: 发起人头像
              example: "https://images.example.com/avatar1.jpg"
            location:
              type: string
              description: 发起人位置
              example: "5栋2单元"
        participants:
          type: object
          properties:
            current:
              type: integer
              description: 当前参与人数
              example: 2
            target:
              type: integer
              description: 目标参与人数
              example: 5
            progress:
              type: number
              description: 参与进度百分比
              example: 40.0
        deadline:
          type: string
          format: date-time
          description: 拼单截止时间
          example: "2024-01-16T23:59:59Z"
        remainingTime:
          type: string
          description: 剩余时间描述
          example: "剩余 23:45:30"
        status:
          type: string
          description: 拼单状态
          example: "active"
          enum: ["active", "completed", "expired", "cancelled"]
        publishTime:
          type: string
          format: date-time
          description: 发布时间
          example: "2024-01-15T10:30:00Z"
        isJoined:
          type: boolean
          description: 当前用户是否已参与
          example: false
        canJoin:
          type: boolean
          description: 是否可以参与
          example: true

    GroupBuyDetailResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          $ref: '#/components/schemas/GroupBuyDetail'

    GroupBuyDetail:
      type: object
      properties:
        id:
          type: string
          description: 拼单活动ID
          example: "activity_123456"
        title:
          type: string
          description: 拼单标题
          example: "盒马鲜生水果拼单"
        description:
          type: string
          description: 拼单详细描述
          example: "本周末去盒马采购水果，有需要带的吗？车厘子、葡萄、苹果、橙子都可以，满300免运费。预计周六上午10点出发，有车可以一起去，也可以帮忙代购。"
        category:
          type: string
          description: 拼单分类
          example: "超市果蔬"
        images:
          type: array
          items:
            type: string
          description: 拼单图片列表
          example: ["https://images.example.com/fruit1.jpg", "https://images.example.com/fruit2.jpg"]
        organizer:
          type: object
          properties:
            id:
              type: string
              description: 发起人ID
              example: "user_789"
            name:
              type: string
              description: 发起人姓名
              example: "王小花"
            avatar:
              type: string
              description: 发起人头像
              example: "https://images.example.com/avatar1.jpg"
            location:
              type: string
              description: 发起人位置
              example: "5栋2单元"
            phone:
              type: string
              description: 联系电话（脱敏）
              example: "138****6789"
            wechat:
              type: string
              description: 微信号
              example: "wangxiaohua123"
        participants:
          type: object
          properties:
            current:
              type: integer
              description: 当前参与人数
              example: 2
            target:
              type: integer
              description: 目标参与人数
              example: 5
            progress:
              type: number
              description: 参与进度百分比
              example: 40.0
            list:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: 参与者ID
                    example: "user_456"
                  name:
                    type: string
                    description: 参与者姓名
                    example: "李小明"
                  avatar:
                    type: string
                    description: 参与者头像
                    example: "https://images.example.com/avatar2.jpg"
                  location:
                    type: string
                    description: 参与者位置
                    example: "3栋1单元"
                  joinTime:
                    type: string
                    format: date-time
                    description: 参与时间
                    example: "2024-01-15T11:30:00Z"
        timeline:
          type: object
          properties:
            publishTime:
              type: string
              format: date-time
              description: 发布时间
              example: "2024-01-15T10:30:00Z"
            deadline:
              type: string
              format: date-time
              description: 拼单截止时间
              example: "2024-01-16T23:59:59Z"
            executionTime:
              type: string
              format: date-time
              description: 执行时间
              example: "2024-01-17T10:00:00Z"
              nullable: true
        requirements:
          type: object
          properties:
            minParticipants:
              type: integer
              description: 最少参与人数
              example: 3
            maxParticipants:
              type: integer
              description: 最多参与人数
              example: 5
            location:
              type: string
              description: 集合地点
              example: "小区南门"
            notes:
              type: string
              description: 特殊要求
              example: "请提前联系确认需要购买的商品"
        status:
          type: string
          description: 拼单状态
          example: "active"
          enum: ["active", "completed", "expired", "cancelled"]
        remainingTime:
          type: string
          description: 剩余时间描述
          example: "剩余 23:45:30"
        viewCount:
          type: integer
          description: 浏览次数
          example: 156
        isJoined:
          type: boolean
          description: 当前用户是否已参与
          example: false
        canJoin:
          type: boolean
          description: 是否可以参与
          example: true
        canEdit:
          type: boolean
          description: 是否可以编辑（仅发起人）
          example: false

    PublishGroupBuyRequest:
      type: object
      required:
        - title
        - description
        - category
        - deadline
        - maxParticipants
      properties:
        title:
          type: string
          description: 拼单标题
          example: "盒马鲜生水果拼单"
          maxLength: 100
        description:
          type: string
          description: 拼单详细描述
          example: "本周末去盒马采购水果，有需要带的吗？车厘子、葡萄、苹果、橙子都可以，满300免运费。"
          maxLength: 1000
        category:
          type: string
          description: 拼单分类
          example: "超市果蔬"
          enum: ["超市果蔬", "生鲜海鲜", "电商商品", "餐饮外卖", "其他"]
        images:
          type: array
          items:
            type: string
          description: 拼单图片列表
          example: ["https://images.example.com/fruit1.jpg", "https://images.example.com/fruit2.jpg"]
          maxItems: 9
        deadline:
          type: string
          format: date-time
          description: 拼单截止时间
          example: "2024-01-16T23:59:59Z"
        executionTime:
          type: string
          format: date-time
          description: 执行时间
          example: "2024-01-17T10:00:00Z"
          nullable: true
        requirements:
          type: object
          properties:
            minParticipants:
              type: integer
              description: 最少参与人数
              example: 3
              minimum: 1
            maxParticipants:
              type: integer
              description: 最多参与人数
              example: 5
              minimum: 2
            location:
              type: string
              description: 集合地点
              example: "小区南门"
            notes:
              type: string
              description: 特殊要求
              example: "请提前联系确认需要购买的商品"
              maxLength: 500
        contactInfo:
          type: object
          properties:
            phone:
              type: string
              description: 联系电话
              example: "13800138000"
              pattern: '^1[3-9]\d{9}$'
            wechat:
              type: string
              description: 微信号
              example: "wangxiaohua123"
      example:
        title: "盒马鲜生水果拼单"
        description: "本周末去盒马采购水果，有需要带的吗？车厘子、葡萄、苹果、橙子都可以，满300免运费。"
        category: "超市果蔬"
        images: ["https://images.example.com/fruit1.jpg", "https://images.example.com/fruit2.jpg"]
        deadline: "2024-01-16T23:59:59Z"
        executionTime: "2024-01-17T10:00:00Z"
        requirements:
          minParticipants: 3
          maxParticipants: 5
          location: "小区南门"
          notes: "请提前联系确认需要购买的商品"
        contactInfo:
          phone: "13800138000"
          wechat: "wangxiaohua123"

    PublishGroupBuyResponse:
      type: object
      properties:
        code:
          type: integer
          example: 201
          description: 响应状态码
        message:
          type: string
          example: "拼单活动发布成功"
          description: 响应消息
        data:
          type: object
          properties:
            activityId:
              type: string
              description: 新创建的拼单活动ID
              example: "activity_789012"
            status:
              type: string
              description: 拼单状态
              example: "active"
              enum: ["active", "pending_review"]
            publishTime:
              type: string
              format: date-time
              description: 发布时间
              example: "2024-01-15T10:30:00Z"

    GroupBuySearchRequest:
      type: object
      properties:
        keyword:
          type: string
          description: 搜索关键词
          example: "水果"
          maxLength: 100
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
        filters:
          type: object
          properties:
            category:
              type: string
              description: 拼单分类
              example: "超市果蔬"
            status:
              type: string
              description: 拼单状态
              example: "active"
              enum: ["all", "active", "completed", "expired"]
      example:
        keyword: "水果"
        page: 1
        pageSize: 20
        filters:
          category: "超市果蔬"
          status: "active"

    JoinGroupBuyRequest:
      type: object
      properties:
        message:
          type: string
          description: 参与留言
          example: "我也想要买车厘子，可以一起吗？"
          maxLength: 200
        contactInfo:
          type: object
          properties:
            phone:
              type: string
              description: 联系电话
              example: "13900139000"
              pattern: '^1[3-9]\d{9}$'
            wechat:
              type: string
              description: 微信号
              example: "lixiaoming456"
      example:
        message: "我也想要买车厘子，可以一起吗？"
        contactInfo:
          phone: "13900139000"
          wechat: "lixiaoming456"

    JoinGroupBuyResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "参与拼单成功"
          description: 响应消息
        data:
          type: object
          properties:
            participantId:
              type: string
              description: 参与者ID
              example: "participant_456789"
            joinTime:
              type: string
              format: date-time
              description: 参与时间
              example: "2024-01-15T11:30:00Z"
            currentParticipants:
              type: integer
              description: 当前参与人数
              example: 3
            organizerContact:
              type: object
              properties:
                name:
                  type: string
                  description: 发起人姓名
                  example: "王小花"
                phone:
                  type: string
                  description: 发起人电话（脱敏）
                  example: "138****6789"
                wechat:
                  type: string
                  description: 发起人微信号
                  example: "wangxiaohua123"

    ParticipantsResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            total:
              type: integer
              description: 总参与人数
              example: 3
            participants:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: 参与者ID
                    example: "user_456"
                  name:
                    type: string
                    description: 参与者姓名
                    example: "李小明"
                  avatar:
                    type: string
                    description: 参与者头像
                    example: "https://images.example.com/avatar2.jpg"
                  location:
                    type: string
                    description: 参与者位置
                    example: "3栋1单元"
                  joinTime:
                    type: string
                    format: date-time
                    description: 参与时间
                    example: "2024-01-15T11:30:00Z"
                  message:
                    type: string
                    description: 参与留言
                    example: "我也想要买车厘子，可以一起吗？"
                  contactInfo:
                    type: object
                    properties:
                      phone:
                        type: string
                        description: 联系电话（脱敏）
                        example: "139****9000"
                      wechat:
                        type: string
                        description: 微信号
                        example: "lixiaoming456"

    CategoriesResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            categories:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: 分类ID
                    example: "category_grocery"
                  name:
                    type: string
                    description: 分类名称
                    example: "超市果蔬"
                  icon:
                    type: string
                    description: 分类图标
                    example: "fas fa-apple-alt"
                  color:
                    type: string
                    description: 分类颜色
                    example: "#4CAF50"
                  activityCount:
                    type: integer
                    description: 该分类下的活动数量
                    example: 25

    MyActivitiesResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            total:
              type: integer
              description: 总记录数
              example: 15
            page:
              type: integer
              description: 当前页码
              example: 1
            pageSize:
              type: integer
              description: 每页数量
              example: 20
            activities:
              type: array
              items:
                type: object
                properties:
                  activity:
                    $ref: '#/components/schemas/GroupBuyItem'
                  role:
                    type: string
                    description: 用户角色
                    example: "organizer"
                    enum: ["organizer", "participant"]
                  joinTime:
                    type: string
                    format: date-time
                    description: 参与时间（仅参与者）
                    example: "2024-01-15T11:30:00Z"
                    nullable: true

    UploadResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "上传成功"
          description: 响应消息
        data:
          type: object
          properties:
            images:
              type: array
              items:
                type: object
                properties:
                  url:
                    type: string
                    description: 图片URL
                    example: "https://images.example.com/groupbuy1.jpg"
                  filename:
                    type: string
                    description: 文件名
                    example: "groupbuy1.jpg"
                  size:
                    type: integer
                    description: 文件大小（字节）
                    example: 1024000

    SuccessResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "操作成功"
          description: 响应消息
        data:
          type: object
          nullable: true
          description: 响应数据（可为空）

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
          example: 400
        message:
          type: string
          description: 错误消息
          example: "请求参数错误"
        error:
          type: string
          description: 详细错误信息
          example: "title字段不能为空"
        timestamp:
          type: string
          format: date-time
          description: 错误发生时间
          example: "2024-01-15T10:30:00Z"

tags:
  - name: 拼单管理
    description: 拼单活动的基本管理功能，包括列表查询、详情获取、发布等
  - name: 拼单搜索
    description: 拼单活动搜索相关功能
  - name: 拼单参与
    description: 用户参与拼单活动相关功能
  - name: 拼单分类
    description: 拼单活动分类管理功能
  - name: 用户拼单
    description: 用户个人拼单活动管理
  - name: 文件上传
    description: 拼单活动图片等文件上传功能
