openapi: 3.0.3
info:
  title: 乐享友邻 - 房源租赁API
  description: 房源租赁平台后端接口文档，包含房源管理、搜索、发布等功能
  version: 1.0.0

servers:
  - url: https://dev-api.hoodly-joy.com/v1
    description: 开发环境

security:
  - BearerAuth: []

paths:
  /houses:
    post:
      tags:
        - 房源管理
      summary: 获取房源列表
      description: 根据筛选条件获取房源列表，支持分页、搜索、筛选等功能
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HouseListRequest'
      responses:
        '200':
          description: 成功获取房源列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HouseListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /houses/{houseId}:
    get:
      tags:
        - 房源管理
      summary: 获取房源详情
      description: 根据房源ID获取房源的详细信息
      parameters:
        - name: houseId
          in: path
          required: true
          description: 房源唯一标识ID
          schema:
            type: string
            example: "house_123456"
      responses:
        '200':
          description: 成功获取房源详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HouseDetailResponse'
        '404':
          description: 房源不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /houses/publish:
    post:
      tags:
        - 房源管理
      summary: 发布房源
      description: 房东发布新的房源信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishHouseRequest'
      responses:
        '201':
          description: 房源发布成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublishHouseResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /houses/search:
    post:
      tags:
        - 房源搜索
      summary: 搜索房源
      description: 根据关键词搜索房源，支持小区名称、地址等搜索
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HouseSearchRequest'
      responses:
        '200':
          description: 搜索成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HouseListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /houses/{houseId}/favorite:
    post:
      tags:
        - 房源收藏
      summary: 收藏房源
      description: 用户收藏感兴趣的房源
      parameters:
        - name: houseId
          in: path
          required: true
          description: 房源唯一标识ID
          schema:
            type: string
            example: "house_123456"
      responses:
        '200':
          description: 收藏成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      tags:
        - 房源收藏
      summary: 取消收藏房源
      description: 用户取消收藏房源
      parameters:
        - name: houseId
          in: path
          required: true
          description: 房源唯一标识ID
          schema:
            type: string
            example: "house_123456"
      responses:
        '200':
          description: 取消收藏成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /houses/{houseId}/appointment:
    post:
      tags:
        - 看房预约
      summary: 预约看房
      description: 租客预约看房时间
      parameters:
        - name: houseId
          in: path
          required: true
          description: 房源唯一标识ID
          schema:
            type: string
            example: "house_123456"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AppointmentRequest'
      responses:
        '201':
          description: 预约成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppointmentResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /upload/images:
    post:
      tags:
        - 文件上传
      summary: 上传房源图片
      description: 上传房源相关图片，支持多张图片上传
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: 图片文件数组，最多9张
                type:
                  type: string
                  enum: [cover, room, kitchen, bathroom, living_room, other]
                  description: 图片类型
      responses:
        '200':
          description: 上传成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadResponse'
        '400':
          description: 上传失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    HouseListRequest:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
        filters:
          type: object
          properties:
            city:
              type: string
              description: 城市
              example: "北京市"
            district:
              type: string
              description: 区域
              example: "朝阳区"
            priceRange:
              type: object
              properties:
                min:
                  type: number
                  description: 最低租金
                  example: 2000
                max:
                  type: number
                  description: 最高租金
                  example: 5000
            roomType:
              type: string
              description: 户型
              example: "2室1厅"
            rentType:
              type: string
              enum: [整租, 合租]
              description: 出租方式
            tags:
              type: array
              items:
                type: string
              description: 特色标签
              example: ["近地铁", "拎包入住"]
        sortBy:
          type: string
          enum: [price_asc, price_desc, time_desc, distance]
          default: time_desc
          description: 排序方式
      example:
        page: 1
        pageSize: 20
        filters:
          city: "北京市"
          district: "朝阳区"
          priceRange:
            min: 2000
            max: 5000
          rentType: "整租"
          tags: ["近地铁"]
        sortBy: "time_desc"

    HouseListResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            total:
              type: integer
              description: 总记录数
              example: 156
            page:
              type: integer
              description: 当前页码
              example: 1
            pageSize:
              type: integer
              description: 每页数量
              example: 20
            totalPages:
              type: integer
              description: 总页数
              example: 8
            houses:
              type: array
              items:
                $ref: '#/components/schemas/HouseItem'

    HouseItem:
      type: object
      properties:
        id:
          type: string
          description: 房源ID
          example: "house_123456"
        title:
          type: string
          description: 房源标题
          example: "阳光花园 2室1厅"
        price:
          type: number
          description: 月租金
          example: 3500
        area:
          type: number
          description: 房屋面积（平方米）
          example: 85
        roomType:
          type: string
          description: 户型
          example: "2室1厅1卫"
        orientation:
          type: string
          description: 朝向
          example: "南北通透"
        decoration:
          type: string
          description: 装修情况
          example: "精装修"
        rentType:
          type: string
          enum: [整租, 合租]
          description: 出租方式
          example: "整租"
        coverImage:
          type: string
          description: 封面图片URL
          example: "https://images.example.com/house1.jpg"
        imageCount:
          type: integer
          description: 图片总数
          example: 12
        tags:
          type: array
          items:
            type: string
          description: 特色标签
          example: ["整租", "拎包入住", "近地铁"]
        location:
          type: object
          properties:
            city:
              type: string
              description: 城市
              example: "北京市"
            district:
              type: string
              description: 区域
              example: "朝阳区"
            address:
              type: string
              description: 详细地址
              example: "阳光花园2号楼"
            subway:
              type: string
              description: 地铁信息
              example: "距离地铁2号线阳光花园站步行5分钟"
        publishTime:
          type: string
          format: date-time
          description: 发布时间
          example: "2024-01-15T10:30:00Z"
        landlord:
          type: object
          properties:
            id:
              type: string
              description: 房东ID
              example: "landlord_789"
            name:
              type: string
              description: 房东姓名
              example: "张房东"
            avatar:
              type: string
              description: 头像URL
              example: "https://images.example.com/avatar1.jpg"
            rating:
              type: number
              description: 评分
              example: 4.8
            rentCount:
              type: integer
              description: 已出租套数
              example: 12
        isFavorite:
          type: boolean
          description: 是否已收藏
          example: false

    HouseDetailResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          $ref: '#/components/schemas/HouseDetail'

    HouseDetail:
      type: object
      properties:
        id:
          type: string
          description: 房源ID
          example: "house_123456"
        title:
          type: string
          description: 房源标题
          example: "阳光花园 2室1厅 南北通透"
        price:
          type: number
          description: 月租金
          example: 3200
        deposit:
          type: integer
          description: 押金（月数）
          example: 1
        paymentType:
          type: string
          description: 付款方式
          example: "押一付三"
        area:
          type: number
          description: 房屋面积（平方米）
          example: 80
        roomType:
          type: string
          description: 户型
          example: "2室1厅1卫"
        orientation:
          type: string
          description: 朝向
          example: "南北通透"
        decoration:
          type: string
          description: 装修情况
          example: "精装修"
        floor:
          type: object
          properties:
            current:
              type: integer
              description: 所在楼层
              example: 5
            total:
              type: integer
              description: 总楼层
              example: 18
        rentType:
          type: string
          enum: [整租, 合租]
          description: 出租方式
          example: "整租"
        availableDate:
          type: string
          format: date
          description: 可入住日期
          example: "2024-02-01"
        minRentPeriod:
          type: integer
          description: 最短租期（月）
          example: 6
        images:
          type: array
          items:
            type: object
            properties:
              url:
                type: string
                description: 图片URL
                example: "https://images.example.com/house1_1.jpg"
              type:
                type: string
                enum: [cover, room, kitchen, bathroom, living_room, other]
                description: 图片类型
                example: "cover"
              description:
                type: string
                description: 图片描述
                example: "客厅"
        facilities:
          type: array
          items:
            type: string
          description: 房屋配置
          example: ["床", "空调", "电视", "宽带", "洗衣机", "冰箱"]
        tags:
          type: array
          items:
            type: string
          description: 特色标签
          example: ["整租", "拎包入住", "近地铁"]
        location:
          type: object
          properties:
            city:
              type: string
              description: 城市
              example: "北京市"
            district:
              type: string
              description: 区域
              example: "朝阳区"
            address:
              type: string
              description: 详细地址
              example: "北京市朝阳区阳光花园2号楼 8层"
            coordinates:
              type: object
              properties:
                latitude:
                  type: number
                  description: 纬度
                  example: 39.9890
                longitude:
                  type: number
                  description: 经度
                  example: 116.4877
            nearbyFacilities:
              type: array
              items:
                type: object
                properties:
                  type:
                    type: string
                    description: 设施类型
                    example: "地铁站"
                  name:
                    type: string
                    description: 设施名称
                    example: "望京南站"
                  distance:
                    type: integer
                    description: 距离（米）
                    example: 800
                  icon:
                    type: string
                    description: 图标类型
                    example: "fas fa-subway"
        description:
          type: string
          description: 房源描述
          example: "房屋位于阳光花园小区，环境优美，交通便利..."
        landlord:
          type: object
          properties:
            id:
              type: string
              description: 房东ID
              example: "landlord_789"
            name:
              type: string
              description: 房东姓名
              example: "张房东"
            avatar:
              type: string
              description: 头像URL
              example: "https://images.example.com/avatar1.jpg"
            rating:
              type: number
              description: 评分
              example: 4.8
            rentCount:
              type: integer
              description: 已出租套数
              example: 12
            phone:
              type: string
              description: 联系电话（脱敏）
              example: "138****6789"
            wechat:
              type: string
              description: 微信号
              example: "house_zhang"
            viewingTime:
              type: string
              description: 看房时间
              example: "周一至周日 9:00-20:00"
        publishTime:
          type: string
          format: date-time
          description: 发布时间
          example: "2024-01-15T10:30:00Z"
        viewCount:
          type: integer
          description: 浏览次数
          example: 256
        isFavorite:
          type: boolean
          description: 是否已收藏
          example: false

    PublishHouseRequest:
      type: object
      required:
        - title
        - price
        - roomType
        - area
        - location
      properties:
        title:
          type: string
          description: 房源标题
          example: "阳光花园 2室1厅 南北通透"
          maxLength: 100
        price:
          type: number
          description: 月租金
          example: 3200
          minimum: 0
        deposit:
          type: integer
          description: 押金（月数）
          example: 1
          minimum: 0
        paymentType:
          type: string
          description: 付款方式
          example: "押一付三"
          enum: ["押一付一", "押一付三", "押一付六", "押二付一", "半年付", "年付"]
        roomType:
          type: object
          properties:
            rooms:
              type: integer
              description: 室数
              example: 2
              minimum: 1
            halls:
              type: integer
              description: 厅数
              example: 1
              minimum: 0
            bathrooms:
              type: integer
              description: 卫数
              example: 1
              minimum: 0
        area:
          type: number
          description: 房屋面积（平方米）
          example: 80
          minimum: 10
        floor:
          type: object
          properties:
            current:
              type: integer
              description: 所在楼层
              example: 5
              minimum: 1
            total:
              type: integer
              description: 总楼层
              example: 18
              minimum: 1
        orientation:
          type: string
          description: 朝向
          example: "南北通透"
          enum: ["南", "北", "东", "西", "东南", "西南", "东北", "西北", "南北通透"]
        decoration:
          type: string
          description: 装修情况
          example: "精装修"
          enum: ["精装修", "简装修", "毛坯"]
        rentType:
          type: string
          enum: [整租, 合租]
          description: 出租方式
          example: "整租"
        availableDate:
          type: string
          format: date
          description: 可入住日期
          example: "2024-02-01"
        minRentPeriod:
          type: integer
          description: 最短租期（月）
          example: 6
          minimum: 1
        images:
          type: array
          items:
            type: object
            properties:
              url:
                type: string
                description: 图片URL
                example: "https://images.example.com/house1_1.jpg"
              type:
                type: string
                enum: [cover, room, kitchen, bathroom, living_room, other]
                description: 图片类型
                example: "cover"
              description:
                type: string
                description: 图片描述
                example: "客厅"
          maxItems: 9
        facilities:
          type: array
          items:
            type: string
          description: 房屋配置
          example: ["床", "空调", "电视", "宽带", "洗衣机", "冰箱"]
        tags:
          type: array
          items:
            type: string
          description: 特色标签
          example: ["近地铁", "拎包入住", "随时看房"]
        location:
          type: object
          required:
            - city
            - district
            - address
            - coordinates
          properties:
            province:
              type: string
              description: 省份
              example: "北京市"
            city:
              type: string
              description: 城市
              example: "北京市"
            district:
              type: string
              description: 区域
              example: "朝阳区"
            businessArea:
              type: string
              description: 商圈
              example: "望京"
            community:
              type: string
              description: 小区名称
              example: "阳光花园"
            address:
              type: string
              description: 详细地址
              example: "阳光花园2号楼8层"
            coordinates:
              type: object
              required:
                - latitude
                - longitude
              properties:
                latitude:
                  type: number
                  description: 纬度
                  example: 39.9890
                longitude:
                  type: number
                  description: 经度
                  example: 116.4877
            nearbyFacilities:
              type: array
              items:
                type: object
                properties:
                  type:
                    type: string
                    description: 设施类型
                    example: "地铁站"
                  name:
                    type: string
                    description: 设施名称
                    example: "望京南站"
                  distance:
                    type: integer
                    description: 距离（米）
                    example: 800
        description:
          type: string
          description: 房源描述
          example: "房屋位于阳光花园小区，环境优美，交通便利..."
          maxLength: 1000
      example:
        title: "阳光花园 2室1厅 南北通透"
        price: 3200
        deposit: 1
        paymentType: "押一付三"
        roomType:
          rooms: 2
          halls: 1
          bathrooms: 1
        area: 80
        floor:
          current: 5
          total: 18
        orientation: "南北通透"
        decoration: "精装修"
        rentType: "整租"
        availableDate: "2024-02-01"
        minRentPeriod: 6
        facilities: ["床", "空调", "电视", "宽带"]
        tags: ["近地铁", "拎包入住"]
        location:
          province: "北京市"
          city: "北京市"
          district: "朝阳区"
          businessArea: "望京"
          community: "阳光花园"
          address: "阳光花园2号楼8层"
          coordinates:
            latitude: 39.9890
            longitude: 116.4877
        description: "房屋位于阳光花园小区，环境优美，交通便利..."

    PublishHouseResponse:
      type: object
      properties:
        code:
          type: integer
          example: 201
          description: 响应状态码
        message:
          type: string
          example: "房源发布成功"
          description: 响应消息
        data:
          type: object
          properties:
            houseId:
              type: string
              description: 新创建的房源ID
              example: "house_789012"
            status:
              type: string
              description: 房源状态
              example: "pending_review"
              enum: ["pending_review", "published", "rejected"]
            publishTime:
              type: string
              format: date-time
              description: 发布时间
              example: "2024-01-15T10:30:00Z"

    HouseSearchRequest:
      type: object
      properties:
        keyword:
          type: string
          description: 搜索关键词（小区名称、地址等）
          example: "阳光花园"
          maxLength: 100
        page:
          type: integer
          minimum: 1
          default: 1
          description: 页码
        pageSize:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
          description: 每页数量
        filters:
          type: object
          properties:
            city:
              type: string
              description: 城市
              example: "北京市"
            district:
              type: string
              description: 区域
              example: "朝阳区"
            priceRange:
              type: object
              properties:
                min:
                  type: number
                  description: 最低租金
                  example: 2000
                max:
                  type: number
                  description: 最高租金
                  example: 5000
      example:
        keyword: "阳光花园"
        page: 1
        pageSize: 20
        filters:
          city: "北京市"
          district: "朝阳区"

    AppointmentRequest:
      type: object
      required:
        - appointmentDate
        - appointmentTime
        - contactPhone
      properties:
        appointmentDate:
          type: string
          format: date
          description: 预约看房日期
          example: "2024-01-20"
        appointmentTime:
          type: string
          description: 预约看房时间段
          example: "14:00-16:00"
        contactPhone:
          type: string
          description: 联系电话
          example: "13800138000"
          pattern: '^1[3-9]\d{9}$'
        message:
          type: string
          description: 留言
          example: "希望能尽快看房"
          maxLength: 200
      example:
        appointmentDate: "2024-01-20"
        appointmentTime: "14:00-16:00"
        contactPhone: "13800138000"
        message: "希望能尽快看房"

    AppointmentResponse:
      type: object
      properties:
        code:
          type: integer
          example: 201
          description: 响应状态码
        message:
          type: string
          example: "预约成功"
          description: 响应消息
        data:
          type: object
          properties:
            appointmentId:
              type: string
              description: 预约ID
              example: "appointment_456789"
            status:
              type: string
              description: 预约状态
              example: "pending"
              enum: ["pending", "confirmed", "cancelled", "completed"]
            appointmentDate:
              type: string
              format: date
              description: 预约日期
              example: "2024-01-20"
            appointmentTime:
              type: string
              description: 预约时间段
              example: "14:00-16:00"
            landlordContact:
              type: object
              properties:
                name:
                  type: string
                  description: 房东姓名
                  example: "张房东"
                phone:
                  type: string
                  description: 房东电话（脱敏）
                  example: "138****6789"

    UploadResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "上传成功"
          description: 响应消息
        data:
          type: object
          properties:
            images:
              type: array
              items:
                type: object
                properties:
                  url:
                    type: string
                    description: 图片URL
                    example: "https://images.example.com/house1_1.jpg"
                  filename:
                    type: string
                    description: 文件名
                    example: "house1_1.jpg"
                  size:
                    type: integer
                    description: 文件大小（字节）
                    example: 1024000
                  type:
                    type: string
                    description: 图片类型
                    example: "cover"

    SuccessResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "操作成功"
          description: 响应消息
        data:
          type: object
          nullable: true
          description: 响应数据（可为空）

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
          example: 400
        message:
          type: string
          description: 错误消息
          example: "请求参数错误"
        error:
          type: string
          description: 详细错误信息
          example: "price字段不能为空"
        timestamp:
          type: string
          format: date-time
          description: 错误发生时间
          example: "2024-01-15T10:30:00Z"

tags:
  - name: 房源管理
    description: 房源的基本管理功能，包括列表查询、详情获取、发布等
  - name: 房源搜索
    description: 房源搜索相关功能
  - name: 房源收藏
    description: 用户收藏房源相关功能
  - name: 看房预约
    description: 租客预约看房相关功能
  - name: 文件上传
    description: 图片等文件上传功能
