openapi: 3.0.3
info:
  title: 乐享友邻停车模块API
  description: 停车位租售市场模块的RESTful API规范
  version: 1.0.0


servers:
  - url: https://api.hoodlyjoy.com/v1
    description: 生产环境服务器
  - url: https://staging-api.hoodlyjoy.com/v1
    description: 测试环境服务器
  - url: http://localhost:3000/v1
    description: 开发环境服务器

tags:
  - name: parking-spaces
    description: 停车位管理相关操作
  - name: search
    description: 搜索和筛选相关操作
  - name: users
    description: 用户和业主信息相关操作
  - name: communities
    description: 小区和位置数据相关操作
  - name: images
    description: 图片上传和管理相关操作

paths:
  /parking-spaces:
    post:
      tags:
        - parking-spaces
      summary: 搜索停车位
      description: |
        搜索并获取停车位的分页列表，支持复杂的筛选条件。
        使用POST方法以支持可能超出URL长度限制的复杂查询参数。
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchParkingSpacesRequest'
      responses:
        '200':
          description: 成功获取停车位列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchParkingSpacesResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      tags:
        - parking-spaces
      summary: 发布新的停车位
      description: 发布一个新的停车位用于出租或出售
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateParkingSpaceRequest'
      responses:
        '201':
          description: 停车位发布成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParkingSpaceDetailResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /parking-spaces/{id}:
    get:
      tags:
        - parking-spaces
      summary: 获取停车位详情
      description: 获取指定停车位的详细信息
      parameters:
        - name: id
          in: path
          required: true
          description: 停车位ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 成功获取停车位详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParkingSpaceDetailResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    patch:
      tags:
        - parking-spaces
      summary: 更新停车位信息
      description: 更新现有停车位的发布信息
      parameters:
        - name: id
          in: path
          required: true
          description: 停车位ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateParkingSpaceRequest'
      responses:
        '200':
          description: 停车位信息更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/ParkingSpaceDetail'
                  message:
                    type: string
                    example: "停车位信息更新成功"
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      tags:
        - parking-spaces
      summary: 删除停车位
      description: 删除停车位发布信息
      parameters:
        - name: id
          in: path
          required: true
          description: 停车位ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 停车位删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleSuccessResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /parking-spaces/{id}/views:
    post:
      tags:
        - parking-spaces
      summary: 记录停车位浏览
      description: 增加停车位的浏览次数统计
      parameters:
        - name: id
          in: path
          required: true
          description: 停车位ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 浏览记录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ViewRecordResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /parking-spaces/{id}/favorites:
    post:
      tags:
        - parking-spaces
      summary: 添加到收藏
      description: 将停车位添加到用户收藏列表
      parameters:
        - name: id
          in: path
          required: true
          description: 停车位ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 添加收藏成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FavoriteResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      tags:
        - parking-spaces
      summary: 取消收藏
      description: 从用户收藏列表中移除停车位
      parameters:
        - name: id
          in: path
          required: true
          description: 停车位ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 取消收藏成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FavoriteResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /search/suggestions:
    post:
      tags:
        - search
      summary: 获取搜索建议
      description: |
        获取搜索自动完成建议，支持复杂的搜索条件和多种建议类型。
        使用POST方法以支持更复杂的搜索参数。
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchSuggestionsRequest'
      responses:
        '200':
          description: 成功获取搜索建议
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchSuggestionsResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /users/{id}:
    get:
      tags:
        - users
      summary: 获取用户资料
      description: 获取用户/业主的个人资料信息
      parameters:
        - name: id
          in: path
          required: true
          description: 用户ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 成功获取用户资料
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /communities:
    post:
      tags:
        - communities
      summary: 获取小区列表
      description: |
        获取可用小区/住宅区列表，支持复杂的搜索和筛选条件。
        使用POST方法以支持更灵活的查询参数。
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchCommunitiesRequest'
      responses:
        '200':
          description: 成功获取小区列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchCommunitiesResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /communities/{id}/parking-layout:
    get:
      tags:
        - communities
      summary: 获取停车位布局
      description: 获取指定小区的停车位布局/地图信息
      parameters:
        - name: id
          in: path
          required: true
          description: 小区ID
          schema:
            type: string
            format: uuid
        - name: floor
          in: query
          description: 停车场楼层
          required: false
          schema:
            type: string
            enum: [underground-1, underground-2, underground-3, ground]
            default: underground-1
      responses:
        '200':
          description: 成功获取停车位布局
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParkingLayoutResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /images/upload:
    post:
      tags:
        - images
      summary: 上传图片
      description: 上传停车位相关图片
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                images:
                  type: array
                  items:
                    type: string
                    format: binary
                  maxItems: 6
                  description: 图片文件（最多6张）
                parking_space_id:
                  type: string
                  format: uuid
                  description: 关联的停车位ID（新发布时可选）
      responses:
        '200':
          description: 图片上传成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageUploadResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '413':
          description: 文件过大
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  schemas:
    # Request Schemas
    SearchParkingSpacesRequest:
      type: object
      properties:
        pagination:
          type: object
          properties:
            page:
              type: integer
              minimum: 1
              default: 1
              description: 分页页码
            limit:
              type: integer
              minimum: 1
              maximum: 100
              default: 20
              description: 每页条目数量
        search:
          type: object
          properties:
            keywords:
              type: array
              items:
                type: string
              description: 搜索关键词（小区名称、位置或其他关键词）
              example: ["阳光花园", "地下停车位"]
            search_fields:
              type: array
              items:
                type: string
                enum: [title, description, community_name, address]
              default: ["title", "community_name"]
              description: 搜索字段范围
        filters:
          type: object
          properties:
            areas:
              type: array
              items:
                type: string
              description: 按区域/行政区筛选
              example: ["拱墅区", "西湖区"]
            price_range:
              type: object
              properties:
                min:
                  type: number
                  format: float
                  description: 最低价格筛选
                max:
                  type: number
                  format: float
                  description: 最高价格筛选
              example:
                min: 200
                max: 500
            deal_types:
              type: array
              items:
                type: string
                enum: [rent, sale]
              description: 按交易类型筛选
              example: ["rent"]
            parking_types:
              type: array
              items:
                type: string
                enum: [underground, ground, fixed, floating]
              description: 按停车位类型筛选
              example: ["fixed", "underground"]
            features:
              type: array
              items:
                type: string
              description: 按特色功能筛选
              example: ["24小时保安", "有充电桩", "临近电梯"]
            communities:
              type: array
              items:
                type: string
                format: uuid
              description: 按指定小区筛选
              example: ["123e4567-e89b-12d3-a456-************"]
            status:
              type: array
              items:
                type: string
                enum: [available, rented, sold, pending]
              default: ["available"]
              description: 按可用状态筛选
        sorting:
          type: object
          properties:
            field:
              type: string
              enum: [price, distance, date_published, popularity, view_count]
              default: "date_published"
              description: 排序字段
            direction:
              type: string
              enum: [asc, desc]
              default: "desc"
              description: 排序方向
      example:
        pagination:
          page: 1
          limit: 20
        search:
          keywords: ["阳光花园"]
          search_fields: ["title", "community_name"]
        filters:
          areas: ["拱墅区"]
          price_range:
            min: 200
            max: 500
          deal_types: ["rent"]
          parking_types: ["fixed"]
          features: ["24小时保安"]
          status: ["available"]
        sorting:
          field: "price"
          direction: "asc"

    SearchSuggestionsRequest:
      type: object
      properties:
        query:
          type: string
          minLength: 1
          description: 搜索关键词
          example: "阳光花园"
        suggestion_types:
          type: array
          items:
            type: string
            enum: [community, location, feature, all]
          default: ["all"]
          description: 建议类型
          example: ["community", "location"]
        limit:
          type: integer
          minimum: 1
          maximum: 20
          default: 10
          description: 返回建议数量限制
        filters:
          type: object
          properties:
            city:
              type: string
              description: 限制城市范围
              example: "杭州市"
            district:
              type: string
              description: 限制区域范围
              example: "拱墅区"
            has_parking:
              type: boolean
              default: true
              description: 只显示有停车位的地点
      required:
        - query
      example:
        query: "阳光"
        suggestion_types: ["community", "location"]
        limit: 10
        filters:
          city: "杭州市"
          has_parking: true

    SearchCommunitiesRequest:
      type: object
      properties:
        search:
          type: object
          properties:
            keywords:
              type: array
              items:
                type: string
              description: 搜索关键词（小区名称、地址等）
              example: ["阳光花园", "花园"]
            search_fields:
              type: array
              items:
                type: string
                enum: [name, address, district]
              default: ["name", "address"]
              description: 搜索字段
        filters:
          type: object
          properties:
            cities:
              type: array
              items:
                type: string
              description: 按城市筛选
              example: ["杭州市", "上海市"]
            districts:
              type: array
              items:
                type: string
              description: 按区域筛选
              example: ["拱墅区", "西湖区"]
            has_parking:
              type: boolean
              default: true
              description: 只显示有停车位的小区
            parking_count_range:
              type: object
              properties:
                min:
                  type: integer
                  description: 最少停车位数量
                max:
                  type: integer
                  description: 最多停车位数量
              example:
                min: 10
                max: 100
        pagination:
          type: object
          properties:
            page:
              type: integer
              minimum: 1
              default: 1
            limit:
              type: integer
              minimum: 1
              maximum: 100
              default: 50
        sorting:
          type: object
          properties:
            field:
              type: string
              enum: [name, parking_count, distance]
              default: "name"
            direction:
              type: string
              enum: [asc, desc]
              default: "asc"
      example:
        search:
          keywords: ["阳光花园"]
          search_fields: ["name", "address"]
        filters:
          cities: ["杭州市"]
          districts: ["拱墅区"]
          has_parking: true
          parking_count_range:
            min: 5
        pagination:
          page: 1
          limit: 20
        sorting:
          field: "name"
          direction: "asc"

    # Response Schemas
    SearchParkingSpacesResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            parking_spaces:
              type: array
              items:
                $ref: '#/components/schemas/ParkingSpaceSummary'
            pagination:
              $ref: '#/components/schemas/Pagination'
            search_metadata:
              type: object
              properties:
                total_matches:
                  type: integer
                  example: 45
                  description: 匹配的总数量
                search_time_ms:
                  type: integer
                  example: 125
                  description: 搜索耗时（毫秒）
                applied_filters_count:
                  type: integer
                  example: 3
                  description: 已应用的筛选条件数量
        message:
          type: string
          example: "停车位列表获取成功"
      required:
        - success
        - data
        - message

    SearchSuggestionsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            suggestions:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    example: "community_001"
                  text:
                    type: string
                    example: "阳光花园"
                  type:
                    type: string
                    enum: [community, location, feature]
                    example: "community"
                  count:
                    type: integer
                    description: 该地点的停车位数量
                    example: 15
                  full_address:
                    type: string
                    description: 完整地址（仅位置类型）
                    example: "杭州市拱墅区阳光花园小区"
            total_count:
              type: integer
              example: 3
        message:
          type: string
          example: "搜索建议获取成功"
      required:
        - success
        - data
        - message

    SearchCommunitiesResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            communities:
              type: array
              items:
                $ref: '#/components/schemas/Community'
            pagination:
              $ref: '#/components/schemas/Pagination'
            search_metadata:
              type: object
              properties:
                total_matches:
                  type: integer
                  example: 25
                search_time_ms:
                  type: integer
                  example: 85
        message:
          type: string
          example: "小区列表获取成功"
      required:
        - success
        - data
        - message

    # Common Response Schemas
    ParkingSpaceDetailResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          $ref: '#/components/schemas/ParkingSpaceDetail'
        message:
          type: string
          example: "停车位详情获取成功"
      required:
        - success
        - data
        - message

    UserProfileResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          $ref: '#/components/schemas/UserProfile'
        message:
          type: string
          example: "用户资料获取成功"
      required:
        - success
        - data
        - message

    ParkingLayoutResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          $ref: '#/components/schemas/ParkingLayout'
        message:
          type: string
          example: "停车位布局获取成功"
      required:
        - success
        - data
        - message

    ImageUploadResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            uploaded_images:
              type: array
              items:
                $ref: '#/components/schemas/ImageInfo'
        message:
          type: string
          example: "图片上传成功"
      required:
        - success
        - data
        - message

    ViewRecordResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            view_count:
              type: integer
              example: 157
              description: 当前浏览次数
        message:
          type: string
          example: "浏览记录成功"
      required:
        - success
        - data
        - message

    FavoriteResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            is_favorited:
              type: boolean
              example: true
              description: 是否已收藏
            favorite_count:
              type: integer
              example: 13
              description: 总收藏数
        message:
          type: string
          example: "收藏操作成功"
      required:
        - success
        - data
        - message

    SimpleSuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "操作成功"
      required:
        - success
        - message

    # Data Model Schemas
    ParkingSpaceSummary:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        title:
          type: string
          example: "阳光花园地下停车位"
        price:
          type: number
          format: float
          example: 300.00
        price_unit:
          type: string
          enum: [monthly, total]
          example: "monthly"
        deal_type:
          type: string
          enum: [rent, sale]
          example: "rent"
        parking_type:
          type: string
          enum: [fixed, floating]
          example: "fixed"
        location:
          type: object
          properties:
            community_name:
              type: string
              example: "阳光花园"
            floor:
              type: string
              example: "地下负一层"
            area:
              type: string
              example: "B区"
        features:
          type: array
          items:
            type: string
          example: ["24小时保安", "可短租", "临近地铁"]
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageInfo'
        distance_to_metro:
          type: string
          example: "距离地铁2号线阳光花园站步行5分钟"
          description: 距离地铁站的描述
        view_count:
          type: integer
          example: 156
          description: 浏览次数
        favorite_count:
          type: integer
          example: 12
          description: 收藏次数
        is_favorited:
          type: boolean
          example: false
          description: 当前用户是否已收藏
        published_at:
          type: string
          format: date-time
          example: "2024-01-18T10:30:00Z"
          description: 发布时间
        status:
          type: string
          enum: [available, rented, sold, pending]
          example: "available"
          description: 停车位状态
      required:
        - id
        - title
        - price
        - price_unit
        - deal_type
        - parking_type
        - location
        - published_at
        - status

    ParkingSpaceDetail:
      allOf:
        - $ref: '#/components/schemas/ParkingSpaceSummary'
        - type: object
          properties:
            description:
              type: string
              example: "车位类型：固定车位，专人管理。安全保障：24小时保安巡逻，全天监控。"
            dimensions:
              type: object
              properties:
                length:
                  type: number
                  format: float
                  example: 5.4
                width:
                  type: number
                  format: float
                  example: 2.4
                unit:
                  type: string
                  example: "meters"
            parking_number:
              type: string
              example: "B04"
            rental_terms:
              type: object
              properties:
                minimum_rental_period:
                  type: string
                  example: "3个月起租"
                deposit:
                  type: string
                  example: "1个月"
                payment_methods:
                  type: array
                  items:
                    type: string
                  example: ["月付", "季付"]
            facilities:
              type: array
              items:
                type: string
              example: ["充电桩（需额外付费）", "24小时监控", "专人管理"]
            owner:
              $ref: '#/components/schemas/UserProfile'
            location_detail:
              type: object
              properties:
                full_address:
                  type: string
                  example: "杭州市拱墅区阳光花园小区地下停车场B区"
                coordinates:
                  type: object
                  properties:
                    latitude:
                      type: number
                      format: float
                      example: 30.2741
                    longitude:
                      type: number
                      format: float
                      example: 120.1551
                nearby_landmarks:
                  type: array
                  items:
                    type: string
                  example: ["地铁2号线阳光花园站", "阳光花园小区"]

    CreateParkingSpaceRequest:
      type: object
      properties:
        title:
          type: string
          example: "阳光花园地下停车位"
        price:
          type: number
          format: float
          example: 300.00
        deal_type:
          type: string
          enum: [rent, sale]
          example: "rent"
        parking_type:
          type: string
          enum: [fixed, floating, other]
          example: "fixed"
        community_id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        floor:
          type: string
          enum: [underground-1, underground-2, underground-3, ground, other]
          example: "underground-1"
        area:
          type: string
          example: "B区"
        parking_number:
          type: string
          example: "B04"
        dimensions:
          type: object
          properties:
            length:
              type: number
              format: float
              example: 5.4
            width:
              type: number
              format: float
              example: 2.4
        features:
          type: array
          items:
            type: string
          example: ["24小时保安", "有充电桩", "临近电梯"]
        description:
          type: string
          example: "车位类型：固定车位，专人管理。安全保障：24小时保安巡逻，全天监控。"
        image_urls:
          type: array
          items:
            type: string
            format: uri
          maxItems: 6
          example: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
        contact_info:
          type: object
          properties:
            contact_name:
              type: string
              example: "张先生"
            phone:
              type: string
              example: "13800138000"
            wechat:
              type: string
              example: "zhang_wechat"
          required:
            - contact_name
            - phone
      required:
        - title
        - price
        - deal_type
        - parking_type
        - community_id
        - floor
        - contact_info

    UpdateParkingSpaceRequest:
      type: object
      properties:
        title:
          type: string
          example: "阳光花园地下停车位"
        price:
          type: number
          format: float
          example: 300.00
        deal_type:
          type: string
          enum: [rent, sale]
          example: "rent"
        parking_type:
          type: string
          enum: [fixed, floating, other]
          example: "fixed"
        floor:
          type: string
          enum: [underground-1, underground-2, underground-3, ground, other]
          example: "underground-1"
        area:
          type: string
          example: "B区"
        parking_number:
          type: string
          example: "B04"
        dimensions:
          type: object
          properties:
            length:
              type: number
              format: float
              example: 5.4
            width:
              type: number
              format: float
              example: 2.4
        features:
          type: array
          items:
            type: string
          example: ["24小时保安", "有充电桩", "临近电梯"]
        description:
          type: string
          example: "车位类型：固定车位，专人管理。安全保障：24小时保安巡逻，全天监控。"
        image_urls:
          type: array
          items:
            type: string
            format: uri
          maxItems: 6
          example: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
        contact_info:
          type: object
          properties:
            contact_name:
              type: string
              example: "张先生"
            phone:
              type: string
              example: "13800138000"
            wechat:
              type: string
              example: "zhang_wechat"
        status:
          type: string
          enum: [available, rented, sold, pending]
          example: "available"

    UserProfile:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "张先生"
        avatar_url:
          type: string
          format: uri
          example: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde"
        is_verified:
          type: boolean
          example: true
        community_owner:
          type: string
          example: "阳光花园业主"
        contact_info:
          type: object
          properties:
            phone:
              type: string
              example: "138****8000"
            wechat:
              type: string
              example: "zhang_wechat"
        joined_at:
          type: string
          format: date-time
          example: "2023-06-15T08:30:00Z"
      required:
        - id
        - name
        - is_verified

    Community:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          example: "阳光花园"
        full_address:
          type: string
          example: "杭州市拱墅区阳光花园小区"
        city:
          type: string
          example: "杭州市"
        district:
          type: string
          example: "拱墅区"
        coordinates:
          type: object
          properties:
            latitude:
              type: number
              format: float
              example: 30.2741
            longitude:
              type: number
              format: float
              example: 120.1551
        parking_spaces_count:
          type: integer
          example: 45
        available_floors:
          type: array
          items:
            type: string
            enum: [underground-1, underground-2, underground-3, ground]
          example: ["underground-1", "underground-2", "ground"]
      required:
        - id
        - name
        - full_address
        - city
        - district

    ParkingLayout:
      type: object
      properties:
        community_id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        floor:
          type: string
          enum: [underground-1, underground-2, underground-3, ground]
          example: "underground-1"
        areas:
          type: array
          items:
            type: object
            properties:
              area_name:
                type: string
                example: "A区"
              spaces:
                type: array
                items:
                  type: object
                  properties:
                    space_id:
                      type: string
                      example: "A01"
                    status:
                      type: string
                      enum: [available, occupied, sold, selected]
                      example: "available"
                    position:
                      type: object
                      properties:
                        x:
                          type: integer
                          example: 1
                        y:
                          type: integer
                          example: 1
        layout_metadata:
          type: object
          properties:
            entrance_position:
              type: object
              properties:
                x:
                  type: integer
                  example: 6
                y:
                  type: integer
                  example: 0
            driving_lanes:
              type: array
              items:
                type: object
                properties:
                  start_x:
                    type: integer
                    example: 0
                  end_x:
                    type: integer
                    example: 12
                  y:
                    type: integer
                    example: 2
      required:
        - community_id
        - floor
        - areas

    ImageInfo:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        url:
          type: string
          format: uri
          example: "https://images.example.com/parking/image1.jpg"
        thumbnail_url:
          type: string
          format: uri
          example: "https://images.example.com/parking/thumb_image1.jpg"
        is_cover:
          type: boolean
          example: true
        upload_time:
          type: string
          format: date-time
          example: "2024-01-18T10:30:00Z"
        file_size:
          type: integer
          example: 1024000
        dimensions:
          type: object
          properties:
            width:
              type: integer
              example: 1920
            height:
              type: integer
              example: 1080
      required:
        - id
        - url
        - is_cover

    Pagination:
      type: object
      properties:
        current_page:
          type: integer
          example: 1
        per_page:
          type: integer
          example: 20
        total_pages:
          type: integer
          example: 5
        total_items:
          type: integer
          example: 95
        has_next:
          type: boolean
          example: true
        has_prev:
          type: boolean
          example: false
      required:
        - current_page
        - per_page
        - total_pages
        - total_items
        - has_next
        - has_prev

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: object
          properties:
            code:
              type: string
              example: "VALIDATION_ERROR"
            message:
              type: string
              example: "Invalid input data"
            details:
              type: array
              items:
                type: object
                properties:
                  field:
                    type: string
                    example: "price"
                  message:
                    type: string
                    example: "Price must be a positive number"
        timestamp:
          type: string
          format: date-time
          example: "2024-01-18T10:30:00Z"
      required:
        - success
        - error
        - timestamp

  responses:
    BadRequest:
      description: 请求参数错误 - 输入数据无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error:
              code: "VALIDATION_ERROR"
              message: "输入数据无效"
              details:
                - field: "price"
                  message: "价格必须是正数"
            timestamp: "2024-01-18T10:30:00Z"

    Unauthorized:
      description: 未授权 - 需要身份验证
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error:
              code: "UNAUTHORIZED"
              message: "需要身份验证"
            timestamp: "2024-01-18T10:30:00Z"

    Forbidden:
      description: 禁止访问 - 权限不足
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error:
              code: "FORBIDDEN"
              message: "您没有权限执行此操作"
            timestamp: "2024-01-18T10:30:00Z"

    NotFound:
      description: 资源未找到
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error:
              code: "NOT_FOUND"
              message: "停车位未找到"
            timestamp: "2024-01-18T10:30:00Z"

    InternalServerError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error:
              code: "INTERNAL_ERROR"
              message: "发生意外错误"
            timestamp: "2024-01-18T10:30:00Z"

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: 用户身份验证的JWT令牌

    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: 服务认证的API密钥

security:
  - BearerAuth: []
  - ApiKeyAuth: []
