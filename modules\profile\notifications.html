<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 消息通知</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 600;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow */
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin-bottom: 12px;
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        
        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }

        /* iOS列表项样式 */
        .ios-list-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 14px 16px;
            border-bottom: 0.5px solid rgba(60,60,67,0.08);
            background-color: var(--ios-systemBackground);
            transition: background-color 0.2s;
        }
        
        .ios-list-item:last-child {
            border-bottom: none;
        }
        
        .ios-list-item:active {
            background-color: rgba(60,60,67,0.05);
        }
        
        .ios-list-label {
            font-size: 16px;
            color: var(--ios-label);
        }
        
        .ios-list-value {
            font-size: 16px;
            color: var(--ios-secondaryLabel);
            display: flex;
            align-items: center;
        }
        
        /* iOS列表标题 */
        .ios-list-header {
            padding: 8px 16px;
            font-size: 13px;
            font-weight: 500;
            text-transform: uppercase;
            color: var(--ios-secondaryLabel);
            letter-spacing: 0.01em;
            margin-bottom: 0;
            margin-top: 16px;
        }
        
        /* iOS开关组件 */
        .ios-switch {
            position: relative;
            display: inline-block;
            width: 51px;
            height: 31px;
            flex-shrink: 0;
        }
        
        .ios-switch input {
            opacity: 0;
            width: 0;
            height: 0;
            position: absolute;
        }
        
        .ios-switch-track {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--ios-light-gray);
            transition: .4s;
            border-radius: 16px;
        }
        
        .ios-switch-track:before {
            position: absolute;
            content: "";
            height: 28px;
            width: 28px;
            left: 2px;
            bottom: 1.5px;
            background-color: white;
            transition: .25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border-radius: 50%;
            box-shadow: 0 1px 4px rgba(0,0,0,0.15);
        }
        
        .ios-switch input:checked + .ios-switch-track {
            background-color: var(--ios-green);
        }
        
        .ios-switch input:checked + .ios-switch-track:before {
            transform: translateX(19px);
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen ios-scroll-indicator smooth-scroll">
            <div class="screen-title">消息通知</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="history.back()" class="ios-button ios-haptic text-[#007AFF]">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <h1 class="text-center font-semibold">消息通知</h1>
                <div></div>
            </div>

            <div class="px-4 py-4 pb-32">
                <!-- 通知设置 -->
                <div class="ios-card ios-fade-in">
                    <div class="ios-list-item">
                        <div class="flex items-start">
                            <div class="w-8 h-8 flex items-center justify-center text-[#FF9500] mr-3">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div>
                                <div class="ios-list-label">接收新消息通知</div>
                                <div class="text-[13px] text-[var(--ios-secondaryLabel)] mt-0.5">关闭后将不会收到新消息提醒</div>
                            </div>
                        </div>
                        <label class="ios-switch">
                            <input type="checkbox" checked>
                            <span class="ios-switch-track"></span>
                        </label>
                    </div>

                    <div class="ios-list-item">
                        <div class="flex items-start">
                            <div class="w-8 h-8 flex items-center justify-center text-[#007AFF] mr-3">
                                <i class="fas fa-volume-up"></i>
                            </div>
                            <div>
                                <div class="ios-list-label">通知声音</div>
                                <div class="text-[13px] text-[var(--ios-secondaryLabel)] mt-0.5">新消息提示音</div>
                            </div>
                        </div>
                        <label class="ios-switch">
                            <input type="checkbox" checked>
                            <span class="ios-switch-track"></span>
                        </label>
                    </div>

                    <div class="ios-list-item">
                        <div class="flex items-start">
                            <div class="w-8 h-8 flex items-center justify-center text-[#5856D6] mr-3">
                                <i class="fas fa-vibrate"></i>
                            </div>
                            <div>
                                <div class="ios-list-label">振动</div>
                                <div class="text-[13px] text-[var(--ios-secondaryLabel)] mt-0.5">新消息振动提醒</div>
                            </div>
                        </div>
                        <label class="ios-switch">
                            <input type="checkbox" checked>
                            <span class="ios-switch-track"></span>
                        </label>
                    </div>
                </div>

                <!-- 消息类型设置 -->
                <div class="ios-list-header">接收以下类型的通知</div>
                <div class="ios-card ios-fade-in" style="animation-delay: 0.1s;">
                    <div class="ios-list-item">
                        <div class="ios-list-label">聊天消息</div>
                        <label class="ios-switch">
                            <input type="checkbox" checked>
                            <span class="ios-switch-track"></span>
                        </label>
                    </div>
                    <div class="ios-list-item">
                        <div class="ios-list-label">系统通知</div>
                        <label class="ios-switch">
                            <input type="checkbox" checked>
                            <span class="ios-switch-track"></span>
                        </label>
                    </div>
                    <div class="ios-list-item">
                        <div class="ios-list-label">交易提醒</div>
                        <label class="ios-switch">
                            <input type="checkbox" checked>
                            <span class="ios-switch-track"></span>
                        </label>
                    </div>
                    <div class="ios-list-item">
                        <div class="ios-list-label">活动推送</div>
                        <label class="ios-switch">
                            <input type="checkbox">
                            <span class="ios-switch-track"></span>
                        </label>
                    </div>
                </div>
                
                <!-- 免打扰时段 -->
                <div class="ios-list-header">免打扰时段</div>
                <div class="ios-card ios-fade-in" style="animation-delay: 0.15s;">
                    <div class="ios-list-item">
                        <div class="ios-list-label">开启勿扰模式</div>
                        <label class="ios-switch">
                            <input type="checkbox" id="dndToggle">
                            <span class="ios-switch-track"></span>
                        </label>
                    </div>
                    <div id="dndOptions" class="hidden">
                        <div class="ios-list-item">
                            <div class="ios-list-label">开始时间</div>
                            <div class="ios-list-value">22:00</div>
                        </div>
                        <div class="ios-list-item">
                            <div class="ios-list-label">结束时间</div>
                            <div class="ios-list-value">07:00</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 勿扰模式切换
            const dndToggle = document.getElementById('dndToggle');
            const dndOptions = document.getElementById('dndOptions');
            
            dndToggle.addEventListener('change', function() {
                if(this.checked) {
                    dndOptions.classList.remove('hidden');
                } else {
                    dndOptions.classList.add('hidden');
                }
            });
            
            // 初始化开关状态
            const switches = document.querySelectorAll('.ios-switch input');
            switches.forEach(switchEl => {
                switchEl.addEventListener('change', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
        });
    </script>
</body>
</html> 