<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no">
    <title>乐享友邻 - 小区选择</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-separator: rgba(60,60,67,0.1);
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: rgba(60,60,67,0.6); /* 60% opacity */
            --ios-tertiaryLabel: rgba(60,60,67,0.3); /* 30% opacity */
            --ios-quaternaryLabel: rgba(60,60,67,0.18); /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
            
            /* iOS圆角半径 */
            --ios-corner-radius-small: 6px;
            --ios-corner-radius-medium: 10px;
            --ios-corner-radius-large: 14px;
            --ios-corner-radius-xl: 18px;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
            touch-action: manipulation;
            user-select: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 状态栏 */
        .ios-device-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 15px;
            font-weight: 600;
            background-color: var(--ios-systemBackground);
            position: sticky;
            top: 0;
            z-index: 40;
        }
        
        /* 导航栏 */
        .ios-nav-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            position: sticky;
            top: 44px;
            z-index: 35;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-nav-bar h1 {
            font-weight: 600;
            font-size: 17px;
            color: var(--ios-label);
        }
        
        .ios-nav-back {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
            display: flex;
            align-items: center;
        }
        
        .ios-nav-right {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
        }
        
        /* iOS搜索框样式 */
        .ios-search-bar-container {
            padding: 8px 16px;
            background-color: var(--ios-systemBackground);
            border-bottom: 0.5px solid var(--ios-separator);
            position: sticky;
            top: 88px;
            z-index: 30;
        }
        
        .ios-search-bar {
            display: flex;
            align-items: center;
            background-color: rgba(118, 118, 128, 0.12);
            border-radius: 10px;
            padding: 0 10px;
            height: 36px;
            transition: all 0.2s ease;
        }
        
        .ios-search-bar:focus-within {
            background-color: rgba(118, 118, 128, 0.16);
        }
        
        .ios-search-input {
            background: transparent;
            border: none;
            font-size: 17px;
            padding: 0 8px;
            flex: 1;
            height: 100%;
            color: var(--ios-label);
            outline: none;
        }
        
        .ios-search-input::placeholder {
            color: var(--ios-secondaryLabel);
            font-size: 17px;
        }
        
        /* iOS卡片样式 */
        .ios-card {
            background-color: var(--ios-card);
            border-radius: var(--ios-corner-radius-medium);
            margin-bottom: 12px;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0,0,0,0.03);
        }
        
        /* iOS标签 */
        .ios-tag {
            display: inline-flex;
            padding: 4px 10px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            background-color: var(--ios-blue);
            color: white;
            margin-right: 6px;
        }
        
        /* iOS段落标题 */
        .ios-section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--ios-secondaryLabel);
            margin-bottom: 12px;
        }
        
        /* iOS小区卡片 */
        .ios-community-card {
            border-radius: var(--ios-corner-radius-medium);
            background-color: var(--ios-card);
            padding: 14px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.03);
            transition: transform 0.2s ease;
        }
        
        .ios-community-card:active {
            transform: scale(0.98);
        }
        
        .ios-community-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--ios-corner-radius-small);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        
        .ios-community-info {
            flex: 1;
        }
        
        .ios-community-name {
            font-weight: 600;
            font-size: 16px;
            color: var(--ios-label);
            margin-bottom: 2px;
        }
        
        .ios-community-address {
            font-size: 14px;
            color: var(--ios-secondaryLabel);
        }
        
        .ios-community-distance {
            font-size: 12px;
            color: var(--ios-tertiaryLabel);
            display: flex;
            align-items: center;
            margin-top: 4px;
        }
        
        /* iOS按钮样式 */
        .ios-outline-button {
            display: inline-flex;
            border: 1px solid var(--ios-blue);
            color: var(--ios-blue);
            border-radius: 16px;
            padding: 6px 14px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .ios-outline-button:active {
            background-color: rgba(0, 122, 255, 0.08);
            transform: scale(0.96);
        }
        
        /* iOS链接按钮 */
        .ios-link-button {
            color: var(--ios-blue);
            font-size: 15px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: opacity 0.2s ease;
        }
        
        .ios-link-button:active {
            opacity: 0.6;
        }
        
        /* iOS当前卡片样式 */
        .ios-current-card {
            background-color: rgba(0, 122, 255, 0.08);
            border-radius: var(--ios-corner-radius-medium);
        }
        
        /* iOS触感反馈 */
        .ios-haptic {
            cursor: pointer;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(8px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in-delay-1 { animation-delay: 0.05s; }
        .ios-fade-in-delay-2 { animation-delay: 0.1s; }
        .ios-fade-in-delay-3 { animation-delay: 0.15s; }
        .ios-fade-in-delay-4 { animation-delay: 0.2s; }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen">
            <div class="screen-title">选择小区</div>
            
            <!-- 设备状态栏 -->
            <div class="ios-device-status-bar">
                <div>9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="ios-nav-bar">
                <button onclick="history.back()" class="ios-button ios-haptic ios-nav-back">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <h1>选择小区</h1>
                <div class="w-16"></div>
            </div>

            <!-- 搜索栏 -->
            <div class="ios-search-bar-container">
                <div class="ios-search-bar">
                    <i class="fas fa-search text-[#8E8E93] text-sm"></i>
                    <input type="search" placeholder="搜索小区名称" class="ios-search-input">
                </div>
            </div>

            <!-- 当前绑定小区 -->
            <div class="px-4 py-5 ios-fade-in ios-fade-in-delay-1">
                <h2 class="ios-section-title">当前绑定小区</h2>
                <div class="ios-community-card ios-current-card">
                    <div class="flex items-center flex-1">
                        <div class="ios-community-icon bg-[#007AFF1A]">
                            <i class="fas fa-building text-[#007AFF] text-xl"></i>
                        </div>
                        <div class="ios-community-info">
                            <h3 class="ios-community-name">春题·杭玥府</h3>
                            <p class="ios-community-address">杭州市拱墅区桃园社区</p>
                        </div>
                    </div>
                    <div class="ios-tag">
                        当前小区
                    </div>
                </div>
            </div>

            <!-- 推荐小区列表 -->
            <div class="px-4 pb-20 ios-fade-in ios-fade-in-delay-2">
                <h2 class="ios-section-title">推荐小区</h2>
                <div class="space-y-3">
                    <!-- 小区项 -->
                    <div class="ios-community-card ios-haptic">
                        <div class="flex items-center flex-1">
                            <div class="ios-community-icon bg-[#F2F2F7]">
                                <i class="fas fa-building text-[#8E8E93] text-xl"></i>
                            </div>
                            <div class="ios-community-info">
                                <h3 class="ios-community-name">久境府</h3>
                                <p class="ios-community-address">杭州市临平新洲路927号</p>
                                <div class="ios-community-distance">
                                    <i class="fas fa-map-marker-alt text-[#FF3B30] text-xs mr-1"></i>
                                    <span>距离您 17.6 公里</span>
                                </div>
                            </div>
                        </div>
                        <button class="ios-outline-button ios-haptic">
                            绑定
                        </button>
                    </div>

                    <div class="ios-community-card ios-haptic">
                        <div class="flex items-center flex-1">
                            <div class="ios-community-icon bg-[#F2F2F7]">
                                <i class="fas fa-building text-[#8E8E93] text-xl"></i>
                            </div>
                            <div class="ios-community-info">
                                <h3 class="ios-community-name">桃源居</h3>
                                <p class="ios-community-address">杭州市拱墅区祥园路56号</p>
                                <div class="ios-community-distance">
                                    <i class="fas fa-map-marker-alt text-[#FF3B30] text-xs mr-1"></i>
                                    <span>距离您 0.8 公里</span>
                                </div>
                            </div>
                        </div>
                        <button class="ios-outline-button ios-haptic">
                            绑定
                        </button>
                    </div>

                    <div class="ios-community-card ios-haptic">
                        <div class="flex items-center flex-1">
                            <div class="ios-community-icon bg-[#F2F2F7]">
                                <i class="fas fa-building text-[#8E8E93] text-xl"></i>
                            </div>
                            <div class="ios-community-info">
                                <h3 class="ios-community-name">翡翠城</h3>
                                <p class="ios-community-address">杭州市拱墅区湖州街200号</p>
                                <div class="ios-community-distance">
                                    <i class="fas fa-map-marker-alt text-[#FF3B30] text-xs mr-1"></i>
                                    <span>距离您 1.5 公里</span>
                                </div>
                            </div>
                        </div>
                        <button class="ios-outline-button ios-haptic">
                            绑定
                        </button>
                    </div>

                    <div class="ios-community-card ios-haptic">
                        <div class="flex items-center flex-1">
                            <div class="ios-community-icon bg-[#F2F2F7]">
                                <i class="fas fa-building text-[#8E8E93] text-xl"></i>
                            </div>
                            <div class="ios-community-info">
                                <h3 class="ios-community-name">金色家园</h3>
                                <p class="ios-community-address">杭州市拱墅区丰潭路450号</p>
                                <div class="ios-community-distance">
                                    <i class="fas fa-map-marker-alt text-[#FF3B30] text-xs mr-1"></i>
                                    <span>距离您 2.1 公里</span>
                                </div>
                            </div>
                        </div>
                        <button class="ios-outline-button ios-haptic">
                            绑定
                        </button>
                    </div>
                </div>
            </div>

            <!-- 申请小区入驻 -->
            <div class="mt-6 mb-8 flex justify-center ios-fade-in ios-fade-in-delay-3">
                <div class="text-center">
                    <p class="text-[#8E8E93] text-sm mb-3">没有找到您的小区？</p>
                    <button onclick="window.location.href='register-community.html'" class="ios-link-button ios-haptic">
                        <i class="fas fa-plus-circle mr-1"></i>
                        申请小区入驻
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 绑定按钮点击事件
            const bindButtons = document.querySelectorAll('.ios-outline-button');
            bindButtons.forEach(button => {
                button.addEventListener('click', function(event) {
                    // 阻止事件冒泡，避免触发卡片的点击事件
                    event.stopPropagation();
                    
                    const communityCard = this.closest('.ios-community-card');
                    const communityName = communityCard.querySelector('.ios-community-name').textContent;
                    
                    // 添加强烈的触感反馈
                    if ('vibrate' in navigator) {
                        navigator.vibrate([5, 15, 5]);
                    }
                    
                    if (confirm(`确定要绑定到${communityName}吗？绑定后将只显示该小区的信息。`)) {
                        // 这里添加绑定逻辑
                        alert('绑定成功！');
                        // 刷新页面或更新UI
                        location.reload();
                    }
                });
            });
            
            // 社区卡片点击事件
            const communityCards = document.querySelectorAll('.ios-community-card');
            communityCards.forEach(card => {
                card.addEventListener('click', function() {
                    if (!this.querySelector('.ios-tag')) {  // 如果不是当前小区
                        const bindButton = this.querySelector('.ios-outline-button');
                        if (bindButton) {
                            // 触发绑定按钮点击事件
                            bindButton.click();
                        }
                    }
                });
            });

            // 搜索功能
            const searchInput = document.querySelector('.ios-search-input');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const communityCards = document.querySelectorAll('.ios-community-card:not(.ios-current-card)');
                
                communityCards.forEach(card => {
                    const name = card.querySelector('.ios-community-name').textContent.toLowerCase();
                    const address = card.querySelector('.ios-community-address').textContent.toLowerCase();
                    
                    if (name.includes(searchTerm) || address.includes(searchTerm)) {
                        card.style.display = '';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
            
            // 输入框焦点效果
            searchInput.addEventListener('focus', function() {
                // 轻微触感反馈
                if ('vibrate' in navigator) {
                    navigator.vibrate(5);
                }
                
                // 添加轻微动画效果
                const searchBar = document.querySelector('.ios-search-bar');
                searchBar.style.transform = 'scale(1.01)';
                searchBar.style.transition = 'transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            });
            
            searchInput.addEventListener('blur', function() {
                const searchBar = document.querySelector('.ios-search-bar');
                searchBar.style.transform = 'scale(1)';
            });
            
            // 确保所有淡入元素最终显示
            setTimeout(() => {
                document.querySelectorAll('.ios-fade-in').forEach(el => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                });
            }, 100);
        });
    </script>
</body>
</html> 