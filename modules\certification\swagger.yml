openapi: 3.0.3
info:
  title: 乐享友邻 - 用户认证API
  description: 用户认证系统后端接口文档，包含登录、注册、密码管理、第三方登录等功能
  version: 1.0.0

servers:
  - url: https://dev-api.hoodly-joy.com/v1
    description: 开发环境

paths:
  /auth/login:
    post:
      tags:
        - 用户认证
      summary: 用户登录
      description: 使用手机号和密码进行用户登录
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 用户名或密码错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '423':
          description: 账户被锁定
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/register:
    post:
      tags:
        - 用户认证
      summary: 用户注册
      description: 使用手机号和验证码进行用户注册
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        '201':
          description: 注册成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: 手机号已注册
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/sms/send:
    post:
      tags:
        - 短信验证
      summary: 发送短信验证码
      description: 向指定手机号发送短信验证码
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendSmsRequest'
      responses:
        '200':
          description: 验证码发送成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendSmsResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '429':
          description: 发送频率过高
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/sms/verify:
    post:
      tags:
        - 短信验证
      summary: 验证短信验证码
      description: 验证手机号和短信验证码的有效性
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerifySmsRequest'
      responses:
        '200':
          description: 验证成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerifySmsResponse'
        '400':
          description: 请求参数错误或验证码错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '410':
          description: 验证码已过期
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/password/reset:
    post:
      tags:
        - 密码管理
      summary: 重置密码
      description: 通过手机号和验证码重置用户密码
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordRequest'
      responses:
        '200':
          description: 密码重置成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 用户不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/password/change:
    put:
      tags:
        - 密码管理
      summary: 修改密码
      description: 用户修改登录密码
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
      responses:
        '200':
          description: 密码修改成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 原密码错误或未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/oauth/{provider}:
    get:
      tags:
        - 第三方登录
      summary: 第三方登录授权
      description: 获取第三方登录授权URL
      parameters:
        - name: provider
          in: path
          required: true
          description: 第三方登录提供商
          schema:
            type: string
            enum: [wechat, qq, alipay]
            example: "wechat"
        - name: redirect_uri
          in: query
          description: 授权成功后的回调地址
          schema:
            type: string
            example: "https://app.hoodly-joy.com/auth/callback"
      responses:
        '200':
          description: 成功获取授权URL
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OAuthUrlResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/oauth/{provider}/callback:
    post:
      tags:
        - 第三方登录
      summary: 第三方登录回调
      description: 处理第三方登录授权回调
      parameters:
        - name: provider
          in: path
          required: true
          description: 第三方登录提供商
          schema:
            type: string
            enum: [wechat, qq, alipay]
            example: "wechat"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OAuthCallbackRequest'
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 授权失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/logout:
    post:
      tags:
        - 用户认证
      summary: 用户登出
      description: 用户登出，清除登录状态
      security:
        - BearerAuth: []
      responses:
        '200':
          description: 登出成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/refresh:
    post:
      tags:
        - 用户认证
      summary: 刷新访问令牌
      description: 使用刷新令牌获取新的访问令牌
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        '200':
          description: 令牌刷新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshTokenResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 刷新令牌无效或已过期
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/profile:
    get:
      tags:
        - 用户信息
      summary: 获取用户信息
      description: 获取当前登录用户的基本信息
      security:
        - BearerAuth: []
      responses:
        '200':
          description: 成功获取用户信息
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileResponse'
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    LoginRequest:
      type: object
      required:
        - phone
        - password
      properties:
        phone:
          type: string
          description: 手机号码
          example: "13800138000"
          pattern: '^1[3-9]\d{9}$'
        password:
          type: string
          description: 登录密码
          example: "password123"
          minLength: 6
          maxLength: 20
        rememberMe:
          type: boolean
          description: 是否记住密码
          example: true
          default: false
        deviceInfo:
          type: object
          properties:
            deviceId:
              type: string
              description: 设备唯一标识
              example: "device_123456"
            deviceType:
              type: string
              description: 设备类型
              example: "mobile"
              enum: [mobile, tablet, desktop]
            platform:
              type: string
              description: 平台信息
              example: "iOS 17.0"
            appVersion:
              type: string
              description: 应用版本
              example: "1.0.0"
      example:
        phone: "13800138000"
        password: "password123"
        rememberMe: true
        deviceInfo:
          deviceId: "device_123456"
          deviceType: "mobile"
          platform: "iOS 17.0"
          appVersion: "1.0.0"

    LoginResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "登录成功"
          description: 响应消息
        data:
          type: object
          properties:
            user:
              type: object
              properties:
                id:
                  type: string
                  description: 用户ID
                  example: "user_123456"
                phone:
                  type: string
                  description: 手机号码（脱敏）
                  example: "138****8000"
                nickname:
                  type: string
                  description: 用户昵称
                  example: "张三"
                avatar:
                  type: string
                  description: 用户头像
                  example: "https://images.example.com/avatar1.jpg"
                status:
                  type: string
                  description: 账户状态
                  example: "active"
                  enum: [active, inactive, suspended]
                isVerified:
                  type: boolean
                  description: 是否已实名认证
                  example: true
                lastLoginTime:
                  type: string
                  format: date-time
                  description: 上次登录时间
                  example: "2024-01-15T10:30:00Z"
            tokens:
              type: object
              properties:
                accessToken:
                  type: string
                  description: 访问令牌
                  example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                refreshToken:
                  type: string
                  description: 刷新令牌
                  example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                expiresIn:
                  type: integer
                  description: 访问令牌过期时间（秒）
                  example: 7200
                tokenType:
                  type: string
                  description: 令牌类型
                  example: "Bearer"
            permissions:
              type: array
              items:
                type: string
              description: 用户权限列表
              example: ["user:read", "user:write", "community:join"]

    RegisterRequest:
      type: object
      required:
        - phone
        - verificationCode
        - password
        - confirmPassword
      properties:
        phone:
          type: string
          description: 手机号码
          example: "13800138000"
          pattern: '^1[3-9]\d{9}$'
        verificationCode:
          type: string
          description: 短信验证码
          example: "123456"
          pattern: '^\d{6}$'
        password:
          type: string
          description: 登录密码
          example: "Password123!"
          minLength: 6
          maxLength: 20
        confirmPassword:
          type: string
          description: 确认密码
          example: "Password123!"
          minLength: 6
          maxLength: 20
        nickname:
          type: string
          description: 用户昵称
          example: "张三"
          maxLength: 20
        agreementAccepted:
          type: boolean
          description: 是否同意用户协议
          example: true
        inviteCode:
          type: string
          description: 邀请码（可选）
          example: "INVITE123"
          maxLength: 20
        deviceInfo:
          type: object
          properties:
            deviceId:
              type: string
              description: 设备唯一标识
              example: "device_123456"
            deviceType:
              type: string
              description: 设备类型
              example: "mobile"
              enum: [mobile, tablet, desktop]
            platform:
              type: string
              description: 平台信息
              example: "iOS 17.0"
      example:
        phone: "13800138000"
        verificationCode: "123456"
        password: "Password123!"
        confirmPassword: "Password123!"
        nickname: "张三"
        agreementAccepted: true
        inviteCode: "INVITE123"
        deviceInfo:
          deviceId: "device_123456"
          deviceType: "mobile"
          platform: "iOS 17.0"

    RegisterResponse:
      type: object
      properties:
        code:
          type: integer
          example: 201
          description: 响应状态码
        message:
          type: string
          example: "注册成功"
          description: 响应消息
        data:
          type: object
          properties:
            user:
              type: object
              properties:
                id:
                  type: string
                  description: 用户ID
                  example: "user_789012"
                phone:
                  type: string
                  description: 手机号码（脱敏）
                  example: "138****8000"
                nickname:
                  type: string
                  description: 用户昵称
                  example: "张三"
                status:
                  type: string
                  description: 账户状态
                  example: "active"
                  enum: [active, inactive, pending_verification]
                createTime:
                  type: string
                  format: date-time
                  description: 注册时间
                  example: "2024-01-15T10:30:00Z"
            tokens:
              type: object
              properties:
                accessToken:
                  type: string
                  description: 访问令牌
                  example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                refreshToken:
                  type: string
                  description: 刷新令牌
                  example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                expiresIn:
                  type: integer
                  description: 访问令牌过期时间（秒）
                  example: 7200
                tokenType:
                  type: string
                  description: 令牌类型
                  example: "Bearer"

    SendSmsRequest:
      type: object
      required:
        - phone
        - type
      properties:
        phone:
          type: string
          description: 手机号码
          example: "13800138000"
          pattern: '^1[3-9]\d{9}$'
        type:
          type: string
          description: 短信类型
          example: "register"
          enum: [register, login, reset_password, change_phone]
        captcha:
          type: object
          properties:
            token:
              type: string
              description: 图形验证码令牌
              example: "captcha_token_123"
            code:
              type: string
              description: 图形验证码
              example: "ABCD"
          description: 图形验证码（防刷机制）
      example:
        phone: "13800138000"
        type: "register"
        captcha:
          token: "captcha_token_123"
          code: "ABCD"

    SendSmsResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "验证码发送成功"
          description: 响应消息
        data:
          type: object
          properties:
            sessionId:
              type: string
              description: 会话ID
              example: "sms_session_123456"
            expiresIn:
              type: integer
              description: 验证码有效期（秒）
              example: 300
            cooldown:
              type: integer
              description: 重新发送冷却时间（秒）
              example: 60
            maskedPhone:
              type: string
              description: 脱敏手机号
              example: "138****8000"

    VerifySmsRequest:
      type: object
      required:
        - phone
        - verificationCode
        - sessionId
      properties:
        phone:
          type: string
          description: 手机号码
          example: "13800138000"
          pattern: '^1[3-9]\d{9}$'
        verificationCode:
          type: string
          description: 短信验证码
          example: "123456"
          pattern: '^\d{6}$'
        sessionId:
          type: string
          description: 短信会话ID
          example: "sms_session_123456"
      example:
        phone: "13800138000"
        verificationCode: "123456"
        sessionId: "sms_session_123456"

    VerifySmsResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "验证成功"
          description: 响应消息
        data:
          type: object
          properties:
            verified:
              type: boolean
              description: 是否验证成功
              example: true
            verifyToken:
              type: string
              description: 验证令牌（用于后续操作）
              example: "verify_token_789012"
            expiresIn:
              type: integer
              description: 验证令牌有效期（秒）
              example: 1800

    ResetPasswordRequest:
      type: object
      required:
        - phone
        - verificationCode
        - newPassword
        - confirmPassword
      properties:
        phone:
          type: string
          description: 手机号码
          example: "13800138000"
          pattern: '^1[3-9]\d{9}$'
        verificationCode:
          type: string
          description: 短信验证码
          example: "123456"
          pattern: '^\d{6}$'
        newPassword:
          type: string
          description: 新密码
          example: "NewPassword123!"
          minLength: 6
          maxLength: 20
        confirmPassword:
          type: string
          description: 确认新密码
          example: "NewPassword123!"
          minLength: 6
          maxLength: 20
        sessionId:
          type: string
          description: 短信会话ID
          example: "sms_session_123456"
      example:
        phone: "13800138000"
        verificationCode: "123456"
        newPassword: "NewPassword123!"
        confirmPassword: "NewPassword123!"
        sessionId: "sms_session_123456"

    ChangePasswordRequest:
      type: object
      required:
        - oldPassword
        - newPassword
        - confirmPassword
      properties:
        oldPassword:
          type: string
          description: 原密码
          example: "OldPassword123!"
          minLength: 6
          maxLength: 20
        newPassword:
          type: string
          description: 新密码
          example: "NewPassword123!"
          minLength: 6
          maxLength: 20
        confirmPassword:
          type: string
          description: 确认新密码
          example: "NewPassword123!"
          minLength: 6
          maxLength: 20
      example:
        oldPassword: "OldPassword123!"
        newPassword: "NewPassword123!"
        confirmPassword: "NewPassword123!"

    OAuthUrlResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            authUrl:
              type: string
              description: 第三方授权URL
              example: "https://open.weixin.qq.com/connect/oauth2/authorize?appid=xxx&redirect_uri=xxx&response_type=code&scope=snsapi_userinfo&state=xxx"
            state:
              type: string
              description: 状态参数
              example: "state_123456"
            expiresIn:
              type: integer
              description: 授权链接有效期（秒）
              example: 600

    OAuthCallbackRequest:
      type: object
      required:
        - code
      properties:
        code:
          type: string
          description: 授权码
          example: "auth_code_123456"
        state:
          type: string
          description: 状态参数
          example: "state_123456"
        error:
          type: string
          description: 错误信息（授权失败时）
          example: "access_denied"
      example:
        code: "auth_code_123456"
        state: "state_123456"

    RefreshTokenRequest:
      type: object
      required:
        - refreshToken
      properties:
        refreshToken:
          type: string
          description: 刷新令牌
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
      example:
        refreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

    RefreshTokenResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "令牌刷新成功"
          description: 响应消息
        data:
          type: object
          properties:
            accessToken:
              type: string
              description: 新的访问令牌
              example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            refreshToken:
              type: string
              description: 新的刷新令牌
              example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            expiresIn:
              type: integer
              description: 访问令牌过期时间（秒）
              example: 7200
            tokenType:
              type: string
              description: 令牌类型
              example: "Bearer"

    UserProfileResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "success"
          description: 响应消息
        data:
          type: object
          properties:
            user:
              type: object
              properties:
                id:
                  type: string
                  description: 用户ID
                  example: "user_123456"
                phone:
                  type: string
                  description: 手机号码（脱敏）
                  example: "138****8000"
                nickname:
                  type: string
                  description: 用户昵称
                  example: "张三"
                avatar:
                  type: string
                  description: 用户头像
                  example: "https://images.example.com/avatar1.jpg"
                gender:
                  type: string
                  description: 性别
                  example: "male"
                  enum: [male, female, unknown]
                birthday:
                  type: string
                  format: date
                  description: 生日
                  example: "1990-01-01"
                location:
                  type: object
                  properties:
                    province:
                      type: string
                      description: 省份
                      example: "浙江省"
                    city:
                      type: string
                      description: 城市
                      example: "杭州市"
                    district:
                      type: string
                      description: 区县
                      example: "拱墅区"
                status:
                  type: string
                  description: 账户状态
                  example: "active"
                  enum: [active, inactive, suspended]
                isVerified:
                  type: boolean
                  description: 是否已实名认证
                  example: true
                createTime:
                  type: string
                  format: date-time
                  description: 注册时间
                  example: "2024-01-15T10:30:00Z"
                lastLoginTime:
                  type: string
                  format: date-time
                  description: 上次登录时间
                  example: "2024-01-15T10:30:00Z"

    SuccessResponse:
      type: object
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "操作成功"
          description: 响应消息
        data:
          type: object
          nullable: true
          description: 响应数据（可为空）

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
          example: 400
        message:
          type: string
          description: 错误消息
          example: "请求参数错误"
        error:
          type: string
          description: 详细错误信息
          example: "手机号格式不正确"
        timestamp:
          type: string
          format: date-time
          description: 错误发生时间
          example: "2024-01-15T10:30:00Z"
        traceId:
          type: string
          description: 请求追踪ID
          example: "trace_123456789"

tags:
  - name: 用户认证
    description: 用户登录、注册、登出等基本认证功能
  - name: 短信验证
    description: 短信验证码发送和验证功能
  - name: 密码管理
    description: 密码重置和修改功能
  - name: 第三方登录
    description: 微信、QQ等第三方登录功能
  - name: 用户信息
    description: 用户基本信息管理功能
