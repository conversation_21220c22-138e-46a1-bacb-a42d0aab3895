.prototype-screen {
    width: 375px;
    height: 812px;
    overflow-y: auto;
    position: relative;
    background: #f5f5f5;
}

/* 自定义滚动条样式 */
.prototype-screen::-webkit-scrollbar {
    width: 4px;
}

.prototype-screen::-webkit-scrollbar-track {
    background: transparent;
}

.prototype-screen::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

.prototype-screen::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
}

.screen-content {
    height: calc(100% - 44px);
    overflow-y: auto;
}

.screen-content::-webkit-scrollbar {
    width: 4px;
}

.screen-content::-webkit-scrollbar-track {
    background: transparent;
}

.screen-content::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

.screen-content::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
}

.status-bar {
    height: 44px;
    background-color: white;
    padding: 12px 20px;
    font-size: 14px;
}

.bottom-bar {
    height: 83px;
    padding-bottom: 20px;
    background-color: white;
    border-top: 1px solid #eee;
}

.prototype-container {
    width: 100%;
    height: 100%;
}

.screen-title {
    display: none;
} 