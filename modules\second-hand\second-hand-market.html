<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no">
    <title>乐享友邻 - 二手闲置市场</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-separator: rgba(60,60,67,0.1);
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: rgba(60,60,67,0.6); /* 60% opacity */
            --ios-tertiaryLabel: rgba(60,60,67,0.3); /* 30% opacity */
            --ios-quaternaryLabel: rgba(60,60,67,0.18); /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
            
            /* iOS圆角半径 */
            --ios-corner-radius-small: 6px;
            --ios-corner-radius-medium: 10px;
            --ios-corner-radius-large: 14px;
            --ios-corner-radius-xl: 18px;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
            touch-action: manipulation;
            user-select: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 状态栏 */
        .ios-device-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 15px;
            font-weight: 600;
            background-color: var(--ios-systemBackground);
            position: sticky;
            top: 0;
            z-index: 40;
        }
        
        /* 导航栏 */
        .ios-nav-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            position: sticky;
            top: 44px;
            z-index: 35;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-nav-bar h1 {
            font-weight: 600;
            font-size: 17px;
            color: var(--ios-label);
        }
        
        .ios-nav-back {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
            display: flex;
            align-items: center;
        }
        
        .ios-nav-right {
            color: var(--ios-blue);
            font-size: 17px;
            font-weight: 400;
        }
        
        /* iOS搜索框样式 */
        .ios-search-bar-container {
            padding: 8px 16px;
            background-color: var(--ios-systemBackground);
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-search-bar {
            display: flex;
            align-items: center;
            background-color: rgba(118, 118, 128, 0.12);
            border-radius: 10px;
            padding: 0 10px;
            height: 36px;
            transition: all 0.2s ease;
        }
        
        .ios-search-bar:focus-within {
            background-color: rgba(118, 118, 128, 0.16);
        }
        
        .ios-search-input {
            background: transparent;
            border: none;
            font-size: 17px;
            padding: 0 8px;
            flex: 1;
            height: 100%;
            color: var(--ios-label);
            outline: none;
        }
        
        .ios-search-input::placeholder {
            color: var(--ios-secondaryLabel);
            font-size: 17px;
        }
        
        /* iOS分类导航 */
        .ios-filter-scroll {
            overflow-x: auto;
            white-space: nowrap;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            -ms-overflow-style: none;
            padding: 10px 16px;
            background-color: var(--ios-systemBackground);
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-filter-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .ios-filter-button {
            display: inline-flex;
            align-items: center;
            padding: 7px 14px;
            margin-right: 8px;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 500;
            color: var(--ios-secondaryLabel);
            background-color: rgba(118, 118, 128, 0.12);
            transition: all 0.2s ease;
        }
        
        .ios-filter-button.active {
            background-color: var(--ios-blue);
            color: white;
        }
        
        .ios-filter-button:active {
            transform: scale(0.95);
            opacity: 0.8;
        }
        
        /* iOS排序选项 */
        .ios-sort-options {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            background-color: var(--ios-systemBackground);
            font-size: 15px;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .ios-sort-option {
            display: flex;
            align-items: center;
            margin-right: 16px;
            padding: 6px 0;
            font-weight: 400;
            position: relative;
        }
        
        .ios-sort-option.active {
            color: var(--ios-blue);
            font-weight: 500;
        }
        
        .ios-sort-option:not(.active) {
            color: var(--ios-secondaryLabel);
        }
        
        /* iOS商品卡片 */
        .ios-product-card {
            background-color: var(--ios-systemBackground);
            border-radius: var(--ios-corner-radius-medium);
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.04), 0 1px 2px rgba(0,0,0,0.02);
            transition: transform 0.15s ease, box-shadow 0.15s ease;
        }
        
        .ios-product-card:active {
            transform: scale(0.98);
            box-shadow: 0 1px 2px rgba(0,0,0,0.02);
        }
        
        .ios-product-image {
            width: 100%;
            aspect-ratio: 1;
            object-fit: cover;
            display: block;
        }
        
        .ios-product-content {
            padding: 10px 12px 12px;
        }
        
        .ios-product-title {
            font-weight: 500;
            font-size: 15px;
            color: var(--ios-label);
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .ios-product-price {
            color: var(--ios-red);
            font-weight: 600;
            font-size: 16px;
        }
        
        .ios-product-desc {
            font-size: 13px;
            color: var(--ios-secondaryLabel);
            margin-bottom: 6px;
        }
        
        .ios-product-meta {
            font-size: 12px;
            color: var(--ios-tertiaryLabel);
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            display: flex;
            justify-content: space-around;
            padding: 10px 0 calc(10px + env(safe-area-inset-bottom));
            box-shadow: 0 -0.5px 0 var(--ios-separator);
            z-index: 100;
        }
        
        .ios-tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 4px 0;
            min-width: 60px;
        }
        
        .ios-tab-icon {
            font-size: 24px;
            margin-bottom: 3px;
            color: var(--ios-tertiaryLabel);
        }
        
        .ios-tab-label {
            font-size: 10px;
            color: var(--ios-tertiaryLabel);
            font-weight: 500;
        }
        
        .ios-tab-item.active .ios-tab-icon,
        .ios-tab-item.active .ios-tab-label {
            color: var(--ios-blue);
        }
        
        .ios-tab-add {
            width: 50px;
            height: 50px;
            background-color: var(--ios-blue);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: -15px;
            color: white;
            box-shadow: 0 3px 8px rgba(0,122,255,0.3);
            transition: transform 0.15s ease, box-shadow 0.15s ease;
        }
        
        .ios-tab-add:active {
            transform: scale(0.92);
            box-shadow: 0 2px 5px rgba(0,122,255,0.2);
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(8px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        .ios-fade-in:nth-child(6) { animation-delay: 0.3s; }
        
        /* iOS触感反馈 */
        .ios-haptic {
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen">
            <div class="screen-title">二手闲置市场</div>
            
            <!-- 设备状态栏 -->
            <div class="ios-device-status-bar">
                <div>9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="ios-nav-bar">
                <button onclick="history.back()" class="ios-button ios-haptic ios-nav-back">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <h1>二手闲置</h1>
                <button class="ios-button ios-haptic ios-nav-right">筛选</button>
            </div>
            
            <!-- 搜索框 -->
            <div class="ios-search-bar-container">
                <div class="ios-search-bar">
                    <i class="fas fa-search text-[#8E8E93] text-sm"></i>
                    <input type="text" placeholder="搜索二手闲置" class="ios-search-input">
                </div>
            </div>
            
            <!-- 分类筛选 -->
            <div class="ios-filter-scroll">
                <button class="ios-filter-button ios-haptic active">
                    全部
                </button>
                <button class="ios-filter-button ios-haptic">
                    手机数码
                </button>
                <button class="ios-filter-button ios-haptic">
                    电器
                </button>
                <button class="ios-filter-button ios-haptic">
                    服装
                </button>
                <button class="ios-filter-button ios-haptic">
                    美妆
                </button>
                <button class="ios-filter-button ios-haptic">
                    运动户外
                </button>
                <button class="ios-filter-button ios-haptic">
                    家居日用
                </button>
                <button class="ios-filter-button ios-haptic">
                    图书
                </button>
            </div>
            
            <!-- 排序筛选 -->
            <div class="ios-sort-options">
                <button class="ios-sort-option ios-haptic active">
                    <span>综合排序</span>
                    <i class="fas fa-chevron-down ml-1 text-xs"></i>
                </button>
                <button class="ios-sort-option ios-haptic">
                    <span>销量</span>
                </button>
                <button class="ios-sort-option ios-haptic">
                    <span>价格</span>
                    <i class="fas fa-sort ml-1 text-xs"></i>
                </button>
                <button class="ios-button ios-haptic ml-auto">
                    <i class="fas fa-sliders-h text-[#8E8E93]"></i>
                </button>
            </div>

            <!-- 商品列表 -->
            <div class="grid grid-cols-2 gap-3 p-3 pb-32">
                <!-- 商品1 -->
                <div class="ios-product-card ios-haptic ios-fade-in">
                    <img src="https://images.unsplash.com/photo-1678911820864-e2c567c655d7" class="ios-product-image">
                    <div class="ios-product-content">
                        <h3 class="ios-product-title">iPhone 14 Pro Max</h3>
                        <p class="ios-product-desc">95新 | 整机保修10个月</p>
                        <div class="flex justify-between items-center">
                            <p class="ios-product-price">¥7999</p>
                            <span class="ios-product-meta">2人想要</span>
                        </div>
                    </div>
                </div>

                <!-- 商品2 -->
                <div class="ios-product-card ios-haptic ios-fade-in">
                    <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff" class="ios-product-image">
                    <div class="ios-product-content">
                        <h3 class="ios-product-title">Nike Air Max</h3>
                        <p class="ios-product-desc">全新 | 42码</p>
                        <div class="flex justify-between items-center">
                            <p class="ios-product-price">¥599</p>
                            <span class="ios-product-meta">5人想要</span>
                        </div>
                    </div>
                </div>

                <!-- 商品3 -->
                <div class="ios-product-card ios-haptic ios-fade-in">
                    <img src="https://images.unsplash.com/photo-1546868871-7041f2a55e12" class="ios-product-image">
                    <div class="ios-product-content">
                        <h3 class="ios-product-title">Apple Watch Series 7</h3>
                        <p class="ios-product-desc">9成新 | 带原装充电器</p>
                        <div class="flex justify-between items-center">
                            <p class="ios-product-price">¥2199</p>
                            <span class="ios-product-meta">8人想要</span>
                        </div>
                    </div>
                </div>

                <!-- 商品4 -->
                <div class="ios-product-card ios-haptic ios-fade-in">
                    <img src="https://images.unsplash.com/photo-1505740420928-5e560c06d30e" class="ios-product-image">
                    <div class="ios-product-content">
                        <h3 class="ios-product-title">Beats 无线耳机</h3>
                        <p class="ios-product-desc">95新 | 音质完美</p>
                        <div class="flex justify-between items-center">
                            <p class="ios-product-price">¥799</p>
                            <span class="ios-product-meta">3人想要</span>
                        </div>
                    </div>
                </div>
                
                <!-- 商品5 -->
                <div class="ios-product-card ios-haptic ios-fade-in">
                    <img src="https://images.unsplash.com/photo-1588872657578-7efd1f1555ed" class="ios-product-image">
                    <div class="ios-product-content">
                        <h3 class="ios-product-title">iPad Pro 11英寸</h3>
                        <p class="ios-product-desc">9成新 | 带键盘保护套</p>
                        <div class="flex justify-between items-center">
                            <p class="ios-product-price">¥4299</p>
                            <span class="ios-product-meta">4人想要</span>
                        </div>
                    </div>
                </div>
                
                <!-- 商品6 -->
                <div class="ios-product-card ios-haptic ios-fade-in">
                    <img src="https://images.unsplash.com/photo-1600080972464-8e5f35f63d08" class="ios-product-image">
                    <div class="ios-product-content">
                        <h3 class="ios-product-title">Nintendo Switch</h3>
                        <p class="ios-product-desc">全新 | 国行</p>
                        <div class="flex justify-between items-center">
                            <p class="ios-product-price">¥1899</p>
                            <span class="ios-product-meta">10人想要</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="fixed bottom-0 left-0 right-0 ios-bottom-nav flex justify-around pt-1.5 pb-6 z-40">
                <button onclick="window.location.href='../navigation/home.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-home text-[#8E8E93] text-[22px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">首页</span>
                </button>
                <button onclick="window.location.href='../navigation/discover.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-compass text-[#8E8E93] text-[22px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">发现</span>
                </button>
                <button onclick="window.location.href='publish-product.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <div class="w-[50px] h-[50px] bg-[#007AFF] rounded-full flex items-center justify-center -mt-5 shadow-lg" style="box-shadow: 0 3px 10px rgba(0,122,255,0.3);">
                        <i class="fas fa-plus text-white text-xl"></i>
                    </div>
                </button>
                <button onclick="window.location.href='../navigation/messages.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-comment text-[#007AFF] text-[22px]"></i>
                    <span class="text-[10px] text-[#007AFF] mt-0.5 font-medium">消息</span>
                </button>
                <button onclick="window.location.href='../profile/profile.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-user text-[#8E8E93] text-[22px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">我的</span>
                </button>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                    
                    // 按钮涟漪效果
                    if (this.classList.contains('ios-sort-option') || 
                        this.classList.contains('ios-filter-button')) {
                        
                        const ripple = document.createElement('div');
                        ripple.classList.add('ripple-effect');
                        ripple.style.position = 'absolute';
                        ripple.style.borderRadius = '50%';
                        ripple.style.transform = 'scale(0)';
                        ripple.style.background = 'rgba(0,0,0,0.05)';
                        ripple.style.width = '100%';
                        ripple.style.height = '100%';
                        ripple.style.left = '0';
                        ripple.style.top = '0';
                        ripple.style.pointerEvents = 'none';
                        
                        // 确保相对定位
                        if (getComputedStyle(this).position !== 'relative' && 
                            getComputedStyle(this).position !== 'absolute') {
                            this.style.position = 'relative';
                        }
                        
                        this.appendChild(ripple);
                        
                        // 触发重绘
                        ripple.offsetWidth;
                        
                        ripple.style.transform = 'scale(1)';
                        ripple.style.opacity = '0';
                        ripple.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                        
                        setTimeout(() => {
                            if (ripple.parentNode === this) {
                                this.removeChild(ripple);
                            }
                        }, 600);
                    }
                });
            });
            
            // 分类筛选按钮交互
            const filterButtons = document.querySelectorAll('.ios-filter-button');
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 平滑滚动确保活动按钮可见
                    const scrollContainer = document.querySelector('.ios-filter-scroll');
                    if (scrollContainer) {
                        const buttonRect = this.getBoundingClientRect();
                        const containerRect = scrollContainer.getBoundingClientRect();
                        
                        // 如果按钮不完全在可见区域内
                        if (buttonRect.right > containerRect.right || buttonRect.left < containerRect.left) {
                            // 计算应该滚动的位置
                            const scrollLeft = this.offsetLeft - (containerRect.width / 2) + (buttonRect.width / 2);
                            scrollContainer.scrollTo({
                                left: scrollLeft,
                                behavior: 'smooth'
                            });
                        }
                    }
                });
            });
            
            // 排序选项交互
            const sortOptions = document.querySelectorAll('.ios-sort-option');
            sortOptions.forEach(option => {
                option.addEventListener('click', function() {
                    sortOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                });
            });
            
            // 当列表有更多项目时延迟加载动画效果
            setTimeout(() => {
                document.querySelectorAll('.ios-fade-in').forEach(el => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                });
            }, 100);
        });
    </script>
</body>
</html> 