<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>邻里拼单 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow */
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .ios-card:active {
            transform: scale(0.98);
            box-shadow: 0 1px 3px rgba(0,0,0,0.02);
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding-bottom: env(safe-area-inset-bottom);
            border-top: none;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: 0.7;
        }
        
        /* iOS搜索框样式 */
        .ios-search {
            border-radius: 10px;
            background-color: var(--ios-light-gray);
            height: 36px;
            transition: background-color 0.2s ease;
        }
        
        .ios-search:focus-within {
            background-color: #E0E0E6;
        }
        
        /* iOS标题文字样式 */
        .ios-section-title {
            font-weight: 600;
            letter-spacing: -0.01em;
            color: var(--ios-label);
            font-size: 20px;
        }
        
        /* iOS标签样式 */
        .ios-tag {
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
            padding: 2px 8px;
            background-color: rgba(0,122,255,0.1);
            color: var(--ios-blue);
        }
        
        /* iOS隔离视图风格 */
        .ios-grouped-section {
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
        }
        
        /* iOS价格文本 */
        .ios-price {
            font-weight: 600;
            color: var(--ios-red);
        }
        
        /* iOS列表分割线样式 */
        .ios-separator {
            height: 0.5px;
            background-color: rgba(60,60,67,0.1);
            margin-left: 16px;
        }

        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 14px;
            font-weight: 500;
            background-color: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }

        /* 搜索栏容器 */
        .ios-search-container {
            position: sticky;
            top: 44px;
            z-index: 20;
            background-color: var(--ios-secondarySystemBackground);
            padding: 12px 16px 8px;
            border-bottom: 0.5px solid rgba(0,0,0,0.05);
        }
        
        /* iOS动画效果 */
        @keyframes ios-ripple {
            0% {
                transform: scale(0.8);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }
        
        /* SF符号效果 */
        .sf-symbol {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            font-size: 14px;
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* 平滑滚动 */
        .smooth-scroll {
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        .ios-fade-in:nth-child(4) { animation-delay: 0.2s; }
        .ios-fade-in:nth-child(5) { animation-delay: 0.25s; }
        
        .prototype-screen {
            background-color: var(--ios-secondarySystemBackground);
        }

        /* 进度条样式 */
        .progress-bar {
            height: 3px;
            background-color: var(--ios-light-gray);
            border-radius: 1.5px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background-color: var(--ios-green);
        }

        /* 拼单计时器 */
        .countdown-timer {
            font-variant-numeric: tabular-nums;
            font-weight: 500;
            color: var(--ios-blue);
        }

        /* 拼单按钮 */
        .join-button {
            background-color: var(--ios-blue);
            color: white;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .join-button:active {
            transform: scale(0.96);
            opacity: 0.9;
        }

        /* 分类标签容器 */
        .category-scroll {
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
        }

        .category-scroll::-webkit-scrollbar {
            display: none;
        }

        /* 发布按钮 */
        .publish-button {
            position: fixed;
            bottom: 80px;
            right: 16px;
            width: 50px;
            height: 50px;
            border-radius: 25px;
            background-color: var(--ios-blue);
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
            transition: all 0.2s ease;
        }

        .publish-button:active {
            transform: scale(0.95);
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
        }
    </style>
</head>
<body class="bg-[#F2F2F7]">
    <div class="prototype-container">
        <div class="prototype-screen smooth-scroll ios-scroll-indicator">
            <div class="screen-title">邻里拼单</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="window.location.href='../navigation/home.html'" class="ios-button ios-haptic">
                    <i class="fas fa-chevron-left text-[#007AFF] mr-1"></i>
                    <span class="text-[#007AFF]">返回</span>
                </button>
                <h1 class="text-center font-semibold">邻里拼单</h1>
                <button onclick="window.location.href='publish-group-buy.html'" class="ios-button ios-haptic text-[#007AFF]">
                    <i class="fas fa-plus text-[#007AFF]"></i>
                </button>
            </div>

            <!-- 搜索栏 -->
            <div class="ios-search-container">
                <div class="flex items-center ios-search px-3">
                    <i class="fas fa-search text-[#8E8E93] text-xs"></i>
                    <input type="text" placeholder="搜索拼单内容" class="ml-2 bg-transparent flex-1 outline-none text-sm h-full">
                </div>
            </div>

            <!-- 分类标签 -->
            <div class="py-3 px-4 overflow-x-auto category-scroll">
                <div class="flex space-x-2 w-max">
                    <button class="px-3 py-1.5 bg-[#007AFF] text-white rounded-full text-sm font-medium ios-button ios-haptic">全部</button>
                    <button class="px-3 py-1.5 bg-[rgba(0,122,255,0.1)] text-[#007AFF] rounded-full text-sm font-medium ios-button ios-haptic">超市果蔬</button>
                    <button class="px-3 py-1.5 bg-[rgba(0,122,255,0.1)] text-[#007AFF] rounded-full text-sm font-medium ios-button ios-haptic">生鲜海鲜</button>
                    <button class="px-3 py-1.5 bg-[rgba(0,122,255,0.1)] text-[#007AFF] rounded-full text-sm font-medium ios-button ios-haptic">电商商品</button>
                    <button class="px-3 py-1.5 bg-[rgba(0,122,255,0.1)] text-[#007AFF] rounded-full text-sm font-medium ios-button ios-haptic">餐饮外卖</button>
                    <button class="px-3 py-1.5 bg-[rgba(0,122,255,0.1)] text-[#007AFF] rounded-full text-sm font-medium ios-button ios-haptic">其他</button>
                </div>
            </div>

            <div class="ios-content-area pb-20">
                <!-- 拼单列表 -->
                <div class="px-4 space-y-4">
                    <!-- 拼单项目1 -->
                    <div class="ios-card p-4 ios-fade-in">
                        <div class="flex items-start justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1494790108377-be9c29b29330')"></div>
                                <div class="ml-2">
                                    <div class="text-[15px] font-medium">王小花</div>
                                    <div class="text-xs text-[#8E8E93]">5栋2单元 · 刚刚发布</div>
                                </div>
                            </div>
                            <div class="ios-tag bg-[rgba(0,122,255,0.1)] text-[#007AFF]">超市果蔬</div>
                        </div>
                        
                        <div class="mt-3">
                            <h3 class="text-base font-semibold">盒马鲜生水果拼单</h3>
                            <p class="text-sm text-[#3C3C43CC] mt-1">本周末去盒马采购水果，有需要带的吗？车厘子、葡萄、苹果、橙子都可以，满300免运费~</p>
                            
                            <div class="mt-3 flex space-x-2">
                                <img src="https://images.unsplash.com/photo-1528821128474-27f963b062bf" class="w-20 h-20 rounded-md object-cover">
                                <img src="https://images.unsplash.com/photo-1561043433-9265f73e685f" class="w-20 h-20 rounded-md object-cover">
                            </div>
                            
                            <div class="mt-3 flex items-center justify-between">
                                <div class="flex-1 mr-4">
                                    <div class="flex justify-between text-xs mb-1">
                                        <span>已拼2/5人</span>
                                        <span class="countdown-timer">剩余 23:45:30</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 40%"></div>
                                    </div>
                                </div>
                                <button class="join-button px-3 py-1.5 text-sm ios-button ios-haptic">参与拼单</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 拼单项目2 -->
                    <div class="ios-card p-4 ios-fade-in">
                        <div class="flex items-start justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d')"></div>
                                <div class="ml-2">
                                    <div class="text-[15px] font-medium">李大壮</div>
                                    <div class="text-xs text-[#8E8E93]">3栋1单元 · 30分钟前</div>
                                </div>
                            </div>
                            <div class="ios-tag bg-[rgba(0,122,255,0.1)] text-[#007AFF]">电商商品</div>
                        </div>
                        
                        <div class="mt-3">
                            <h3 class="text-base font-semibold">拼多多电子产品拼单</h3>
                            <p class="text-sm text-[#3C3C43CC] mt-1">拼多多上有款蓝牙耳机特别便宜，需要拼单才能享受最低价，已经有3个人了，还差一个，有兴趣的速度联系我！</p>
                            
                            <div class="mt-3">
                                <img src="https://images.unsplash.com/photo-1505740420928-5e560c06d30e" class="w-full h-32 rounded-md object-cover">
                            </div>
                            
                            <div class="mt-3 flex items-center justify-between">
                                <div class="flex-1 mr-4">
                                    <div class="flex justify-between text-xs mb-1">
                                        <span>已拼3/4人</span>
                                        <span class="countdown-timer">剩余 3:12:45</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 75%"></div>
                                    </div>
                                </div>
                                <button class="join-button px-3 py-1.5 text-sm ios-button ios-haptic">参与拼单</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 拼单项目3 -->
                    <div class="ios-card p-4 ios-fade-in">
                        <div class="flex items-start justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1544005313-94ddf0286df2')"></div>
                                <div class="ml-2">
                                    <div class="text-[15px] font-medium">张阿姨</div>
                                    <div class="text-xs text-[#8E8E93]">2栋3单元 · 2小时前</div>
                                </div>
                            </div>
                            <div class="ios-tag bg-[rgba(0,122,255,0.1)] text-[#007AFF]">生鲜海鲜</div>
                        </div>
                        
                        <div class="mt-3">
                            <h3 class="text-base font-semibold">海鲜市场拼购</h3>
                            <p class="text-sm text-[#3C3C43CC] mt-1">明天上午去海鲜市场采购，可以带邻居一起去。大闸蟹、鲜虾、鱼类都很新鲜，价格实惠，有兴趣的报名，明天9点楼下集合。</p>
                            
                            <div class="mt-3 flex space-x-2">
                                <img src="https://images.unsplash.com/photo-1615141982883-c7ad0e69fd62" class="w-20 h-20 rounded-md object-cover">
                                <img src="https://images.unsplash.com/photo-1550547660-d9450f859349" class="w-20 h-20 rounded-md object-cover">
                                <img src="https://images.unsplash.com/photo-1498654896293-37aacf113fd9" class="w-20 h-20 rounded-md object-cover">
                            </div>
                            
                            <div class="mt-3 flex items-center justify-between">
                                <div class="flex-1 mr-4">
                                    <div class="flex justify-between text-xs mb-1">
                                        <span>已拼4/6人</span>
                                        <span class="countdown-timer">剩余 15:30:00</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 66.7%"></div>
                                    </div>
                                </div>
                                <button class="join-button px-3 py-1.5 text-sm ios-button ios-haptic">参与拼单</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="fixed bottom-0 left-0 right-0 ios-bottom-nav flex justify-around pt-1.5 pb-6 z-40">
                <button onclick="window.location.href='../navigation/home.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-home text-[#8E8E93] text-[20px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">首页</span>
                </button>
                <button onclick="window.location.href='../navigation/discover.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-compass text-[#8E8E93] text-[20px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">发现</span>
                </button>
                <button class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <div class="w-[50px] h-[50px] bg-[#007AFF] rounded-full flex items-center justify-center -mt-5 shadow-lg" style="box-shadow: 0 3px 10px rgba(0,122,255,0.3);">
                        <i class="fas fa-plus text-white text-xl"></i>
                    </div>
                </button>
                <button onclick="window.location.href='../navigation/messages.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-comment text-[#8E8E93] text-[20px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">消息</span>
                </button>
                <button onclick="window.location.href='../profile/profile.html'" class="flex flex-col items-center px-5 ios-button ios-haptic">
                    <i class="fas fa-user text-[#8E8E93] text-[20px]"></i>
                    <span class="text-[10px] text-[#8E8E93] mt-0.5">我的</span>
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载后执行的脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
        });
    </script>
</body>
</html> 