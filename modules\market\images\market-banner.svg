<svg width="800" height="320" viewBox="0 0 800 320" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="800" height="320" fill="#fcf5e9"/>
  
  <!-- 装饰元素 - 右上角装饰 -->
  <path d="M700,0 Q800,100 800,200 L800,0 Z" fill="#f8e3cf"/>
  
  <!-- 装饰元素 - 左下角装饰 -->
  <path d="M0,320 Q100,250 200,280 L0,320 Z" fill="#f8e3cf"/>
  
  <!-- 农产品图案 - 鸡 -->
  <g transform="translate(600, 180) scale(0.5)">
    <path d="M50,50 Q70,30 90,50 T130,50 Q150,30 170,50 T210,50" fill="none" stroke="#e9a839" stroke-width="5"/>
    <circle cx="80" cy="80" r="60" fill="#e9a839"/>
    <circle cx="65" cy="65" r="10" fill="#fff"/>
    <circle cx="65" cy="65" r="5" fill="#000"/>
    <path d="M80,100 L100,130 L60,130 Z" fill="#d95204"/>
  </g>
  
  <!-- 农产品图案 - 鱼 -->
  <g transform="translate(100, 130) scale(0.6)">
    <path d="M50,50 Q150,0 250,50 Q150,100 50,50 Z" fill="#6abce2"/>
    <circle cx="210" cy="50" r="10" fill="#fff"/>
    <circle cx="210" cy="50" r="5" fill="#000"/>
    <path d="M50,50 L20,30 L20,70 Z" fill="#6abce2"/>
  </g>
  
  <!-- 农产品图案 - 蔬菜 -->
  <g transform="translate(320, 50) scale(0.4)">
    <path d="M50,150 Q50,50 100,50 Q150,50 150,150 Z" fill="#4caf50"/>
    <rect x="95" y="150" width="10" height="100" fill="#8d6e63"/>
    <ellipse cx="100" cy="260" rx="30" ry="10" fill="#8d6e63" opacity="0.3"/>
  </g>
  
  <!-- 农产品图案 - 蛋 -->
  <g transform="translate(450, 200) scale(0.3)">
    <ellipse cx="100" cy="100" rx="70" ry="90" fill="#fff9c4"/>
    <ellipse cx="100" cy="100" rx="60" ry="80" fill="#fff176"/>
  </g>
  
  <!-- 文字装饰 -->
  <text x="400" y="160" font-family="'Arial', sans-serif" font-size="36" font-weight="bold" fill="#d95204" text-anchor="middle" opacity="0.1">邻里集市</text>
  
  <!-- 底部纹理 -->
  <rect x="0" y="300" width="800" height="20" fill="#e0f2f1" opacity="0.3"/>
</svg> 