<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>乐享友邻 - 我的小区</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../styles.css">
    <style>
        /* iOS样式增强 */
        :root {
            /* iOS标准色彩 */
            --ios-blue: #007AFF;
            --ios-gray: #8E8E93;
            --ios-light-gray: #E5E5EA;
            --ios-red: #FF3B30;
            --ios-green: #34C759;
            --ios-pink: #FF2D55;
            --ios-purple: #5856D6;
            --ios-orange: #FF9500;
            --ios-yellow: #FFCC00;
            --ios-teal: #5AC8FA;
            --ios-indigo: #5E5CE6;
            
            /* iOS界面背景色 */
            --ios-systemBackground: #FFFFFF;
            --ios-secondarySystemBackground: #F2F2F7;
            --ios-tertiarySystemBackground: #FFFFFF;
            --ios-groupedBackground: #F2F2F7;
            --ios-card: #FFFFFF;
            
            /* iOS字体颜色 */
            --ios-label: #000000;
            --ios-secondaryLabel: #3C3C43B2; /* 70% opacity */
            --ios-tertiaryLabel: #3C3C434D; /* 30% opacity */
            --ios-quaternaryLabel: #3C3C432E; /* 18% opacity */
            
            /* iOS交互状态 */
            --ios-highlight-alpha: 0.1;
            --ios-pressed-alpha: 0.7;
        }
        
        /* 系统字体和文本渲染 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: var(--ios-secondarySystemBackground);
            color: var(--ios-label);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            line-height: 1.3;
            letter-spacing: -0.01em;
        }
        
        /* iOS按钮效果 */
        .ios-button {
            transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        background-color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            user-select: none;
            -webkit-touch-callout: none;
            position: relative;
            font-weight: 500;
        }
        
        .ios-button:active {
            transform: scale(0.96);
            opacity: var(--ios-pressed-alpha);
        }
        
        /* 顶部状态栏和导航栏 */
        .ios-status-bar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 17px;
            font-weight: 600;
            background-color: rgba(255,255,255,0.98);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            position: sticky;
            top: 0;
            z-index: 30;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        /* iOS卡片样式 */
        .ios-card {
            border-radius: 12px;
            background-color: var(--ios-card);
            overflow: hidden;
            /* iOS Shadow */
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin-bottom: 12px;
        }
        
        /* iOS无限滚动指示器 */
        .ios-scroll-indicator::-webkit-scrollbar {
            display: none;
        }
        
        /* iOS模块载入效果 */
        @keyframes ios-fade-in {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .ios-fade-in {
            animation: ios-fade-in 0.4s ease forwards;
            opacity: 0;
            will-change: transform, opacity;
        }
        
        .ios-fade-in:nth-child(1) { animation-delay: 0.05s; }
        .ios-fade-in:nth-child(2) { animation-delay: 0.1s; }
        .ios-fade-in:nth-child(3) { animation-delay: 0.15s; }
        
        /* iOS点击触感 */
        @media (hover: hover) {
            .ios-haptic:hover {
                cursor: pointer;
            }
        }

        /* iOS区块标题 */
        .ios-section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--ios-label);
            margin-bottom: 12px;
        }
        
        /* iOS社区卡片 */
        .ios-community-card {
            display: flex;
            padding: 16px;
            gap: 16px;
            align-items: center;
            background-color: var(--ios-systemBackground);
            border-radius: 12px;
            box-shadow: 0 1px 5px rgba(0,0,0,0.03), 0 2px 4px rgba(0,0,0,0.02);
            margin-bottom: 16px;
            transition: transform 0.2s ease;
        }
        
        .ios-community-card:active {
            transform: scale(0.98);
        }
        
        .ios-community-card.current {
            background-color: rgba(0, 122, 255, 0.08);
        }
        
        .ios-community-icon {
            width: 48px;
            height: 48px;
            border-radius: 10px;
            background-color: var(--ios-secondarySystemBackground);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--ios-blue);
            flex-shrink: 0;
        }
        
        .ios-community-card.current .ios-community-icon {
            color: white;
            background-color: var(--ios-blue);
        }
        
        .ios-community-content {
            flex: 1;
        }
        
        .ios-community-name {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 2px;
        }
        
        .ios-community-address {
            font-size: 14px;
            color: var(--ios-secondaryLabel);
            margin-bottom: 4px;
        }
        
        .ios-community-unit {
            font-size: 12px;
            color: var(--ios-tertiaryLabel);
            display: flex;
            align-items: center;
        }
        
        .ios-community-badge {
            font-size: 13px;
            font-weight: 500;
            color: white;
            background-color: var(--ios-blue);
            padding: 4px 10px;
            border-radius: 16px;
            white-space: nowrap;
        }
        
        .ios-community-action {
            font-size: 14px;
            font-weight: 500;
            color: var(--ios-blue);
            padding: 6px 16px;
            border: 1px solid var(--ios-blue);
            border-radius: 16px;
            transition: all 0.2s ease;
        }
        
        .ios-community-action:active {
            background-color: rgba(0, 122, 255, 0.1);
            transform: scale(0.96);
        }
        
        /* iOS 添加按钮 */
        .ios-add-button {
            width: 100%;
            padding: 12px 0;
            border-radius: 12px;
            border: 1.5px dashed rgba(60,60,67,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: var(--ios-secondaryLabel);
            font-size: 15px;
            font-weight: 500;
            transition: all 0.2s ease;
            background-color: var(--ios-systemBackground);
        }
        
        .ios-add-button:active {
            background-color: rgba(60,60,67,0.05);
            transform: scale(0.98);
        }
        
        /* iOS底部导航栏 */
        .ios-bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(255,255,255,0.96);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            display: flex;
            justify-content: space-around;
            padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
            box-shadow: 0 -0.5px 0 rgba(0,0,0,0.15);
            z-index: 100;
        }
        
        .ios-tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 4px 0;
            min-width: 60px;
        }
        
        .ios-tab-icon {
            font-size: 22px;
            margin-bottom: 4px;
            color: var(--ios-secondaryLabel);
        }
        
        .ios-tab-label {
            font-size: 10px;
            color: var(--ios-secondaryLabel);
            font-weight: 500;
        }
        
        .ios-tab-item.active .ios-tab-icon,
        .ios-tab-item.active .ios-tab-label {
            color: var(--ios-blue);
        }
        
        .ios-tab-add {
            width: 50px;
            height: 50px;
            background-color: var(--ios-blue);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: -20px;
            color: white;
            box-shadow: 0 2px 6px rgba(0,122,255,0.3);
            transition: transform 0.2s ease;
        }
        
        .ios-tab-add:active {
            transform: scale(0.92);
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <div class="prototype-screen ios-scroll-indicator smooth-scroll">
            <div class="screen-title">我的小区</div>
            <div class="status-bar flex items-center justify-between">
                <div class="text-sm">9:41</div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-full"></i>
                </div>
            </div>
            
            <!-- 顶部导航栏 -->
            <div class="ios-status-bar">
                <button onclick="history.back()" class="ios-button ios-haptic text-[#007AFF]">
                    <i class="fas fa-chevron-left mr-1"></i>
                    <span>返回</span>
                </button>
                <h1 class="text-center font-semibold">我的小区</h1>
                <div></div>
            </div>

            <div class="px-4 py-4 pb-32">
                <!-- 当前小区 -->
                <div class="ios-fade-in">
                    <h2 class="ios-section-title">当前小区</h2>
                    <div class="ios-community-card current ios-haptic">
                        <div class="ios-community-icon">
                            <i class="fas fa-building text-xl"></i>
                        </div>
                        <div class="ios-community-content">
                            <div class="ios-community-name">春题·杭玥府</div>
                            <div class="ios-community-address">杭州市拱墅区景辛路</div>
                            <div class="ios-community-unit">
                                <i class="fas fa-map-marker-alt text-[#FF3B30] mr-1"></i>
                                <span>5栋1单元1302</span>
                            </div>
                        </div>
                        <div class="ios-community-badge">当前小区</div>
                    </div>
                </div>

                <!-- 我的小区列表 -->
                <div class="ios-fade-in" style="animation-delay: 0.1s;">
                    <h2 class="ios-section-title mt-4">我的小区</h2>
                    <div class="space-y-4">
                        <!-- 小区1 -->
                        <div class="ios-community-card ios-haptic">
                            <div class="ios-community-icon">
                                <i class="fas fa-building text-xl"></i>
                            </div>
                            <div class="ios-community-content">
                                <div class="ios-community-name">久境府</div>
                                <div class="ios-community-address">杭州市临平区祥园路</div>
                                <div class="ios-community-unit">
                                    <i class="fas fa-map-marker-alt text-[#FF3B30] mr-1"></i>
                                    <span>1栋1单元801</span>
                                </div>
                            </div>
                            <button class="ios-community-action ios-haptic switch-btn">切换</button>
                        </div>

                        <!-- 小区2 -->
                        <div class="ios-community-card ios-haptic">
                            <div class="ios-community-icon">
                                <i class="fas fa-building text-xl"></i>
                            </div>
                            <div class="ios-community-content">
                                <div class="ios-community-name">久雍府</div>
                                <div class="ios-community-address">杭州市临平区康桥路</div>
                                <div class="ios-community-unit">
                                    <i class="fas fa-map-marker-alt text-[#FF3B30] mr-1"></i>
                                    <span>1栋1单元801</span>
                                </div>
                            </div>
                            <button class="ios-community-action ios-haptic switch-btn">切换</button>
                        </div>
                    </div>
                </div>

                <!-- 添加新小区按钮 -->
                <div class="ios-fade-in mt-6" style="animation-delay: 0.15s;">
                    <button onclick="window.location.href='community-select.html'" class="ios-add-button ios-haptic">
                        <i class="fas fa-plus-circle text-[#007AFF]"></i>
                        <span>添加新小区</span>
                    </button>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="ios-bottom-nav">
                <button class="ios-tab-item ios-haptic" onclick="window.location.href='../navigation/home.html'">
                    <i class="fas fa-home ios-tab-icon"></i>
                    <span class="ios-tab-label">首页</span>
                </button>
                <button class="ios-tab-item ios-haptic" onclick="window.location.href='../navigation/discover.html'">
                    <i class="fas fa-compass ios-tab-icon"></i>
                    <span class="ios-tab-label">发现</span>
                </button>
                <button class="ios-haptic">
                    <div class="ios-tab-add">
                        <i class="fas fa-plus"></i>
                    </div>
                </button>
                <button class="ios-tab-item ios-haptic" onclick="window.location.href='../navigation/messages.html'">
                    <i class="fas fa-comment ios-tab-icon"></i>
                    <span class="ios-tab-label">消息</span>
                </button>
                <button class="ios-tab-item active ios-haptic" onclick="window.location.href='profile.html'">
                    <i class="fas fa-user ios-tab-icon"></i>
                    <span class="ios-tab-label">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加iOS触感反馈
            const hapticElements = document.querySelectorAll('.ios-haptic');
            hapticElements.forEach(element => {
                element.addEventListener('click', function() {
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    }
                });
            });
            
            // 切换小区按钮事件
            const switchButtons = document.querySelectorAll('.switch-btn');
            
            switchButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation(); // 防止触发父元素的点击事件
                    
                    const communityCard = this.closest('.ios-community-card');
                    const communityName = communityCard.querySelector('.ios-community-name').textContent;
                    
                    // 创建iOS风格的确认对话框
                    if (confirm(`确定要切换到${communityName}吗？切换后小区范围内的信息将会更新。`)) {
                        // 添加动画效果
                        const allCards = document.querySelectorAll('.ios-community-card');
                        allCards.forEach(card => {
                            card.classList.remove('current');
                            card.querySelector('.ios-community-icon').style.backgroundColor = '';
                            if (card.querySelector('.ios-community-badge')) {
                                card.querySelector('.ios-community-badge').remove();
                            }
                        });
                        
                        // 更新当前卡片为当前小区
                        communityCard.classList.add('current');
                        communityCard.querySelector('.ios-community-icon').style.backgroundColor = 'var(--ios-blue)';
                        communityCard.querySelector('.ios-community-icon i').style.color = 'white';
                        
                        // 替换"切换"按钮为"当前小区"标签
                        this.remove();
                        const badge = document.createElement('div');
                        badge.className = 'ios-community-badge';
                        badge.textContent = '当前小区';
                        communityCard.appendChild(badge);
                        
                        // 显示成功提示
                        setTimeout(() => {
                            alert(`已成功切换到"${communityName}"！`);
                            // 跳转回主页，显示新小区的信息
                            window.location.href = '../navigation/home.html';
                        }, 300);
                    }
                });
            });
            
            // 卡片点击事件（显示小区详情）
            const communityCards = document.querySelectorAll('.ios-community-card');
            communityCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    if (!e.target.closest('.switch-btn')) { // 如果不是点击切换按钮
                        const communityName = this.querySelector('.ios-community-name').textContent;
                        window.location.href = `community-detail.html?name=${encodeURIComponent(communityName)}`;
                    }
                });
            });
        });
    </script>
</body>
</html> 